---
description: 
globs: 
alwaysApply: false
---
# Java Monorepo：模块发现与依赖分析指南

本指南旨在帮助理解由 Maven 管理的多模块 Java 项目的结构。

## 1. 发现模块

- **主要构建文件**：项目使用 Maven。每个模块的根目录下都会有一个 `[pom.xml](mdc:pom.xml)` 文件。
    - 首先定位整个 monorepo 的根 `[pom.xml](mdc:pom.xml)` 文件。
    - 在父 `[pom.xml](mdc:pom.xml)` 文件中查找 `<modules>` 部分。这里列出了子模块。
    ```xml
    <modules>
        <module>module-a</module>
        <module>../module-b</module>
    </modules>
    ```
- **递归搜索**：要找到所有模块，请系统地搜索工作区中所有的 `[pom.xml](mdc:pom.xml)` 文件。注意嵌套的模块结构。

## 2. 分析依赖关系

- **模块依赖**：在每个模块的 `[pom.xml](mdc:pom.xml)` 文件中，`<dependencies>` 部分列出了其对其他模块或外部库的依赖。
    ```xml
    <dependencies>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>another-module</artifactId>
            <version>${project.version}</version> <!-- 内部模块常见配置 -->
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
    </dependencies>
    ```
- **父 POM**：检查每个 `[pom.xml](mdc:pom.xml)` 中的 `<parent>` 部分。这表明继承自一个父 POM，父 POM 通常管理公共依赖（`<dependencyManagement>`）和插件版本。
    ```xml
    <parent>
        <groupId>com.example</groupId>
        <artifactId>project-parent</artifactId>
        <version>1.0.0</version>
        <relativePath>../project-parent/pom.xml</relativePath>
    </parent>
    ```
- **依赖矩阵**：要构建项目拓扑图（依据“主要目标”），请系统地记录内部模块之间的依赖关系。
- **版本冲突与循环依赖**：在分析依赖关系时，请注意潜在的版本冲突（不同模块需要同一库的不同版本）和模块之间的循环依赖。这些是大型 monorepo 中的常见问题。

## 3. 识别核心模块与工具模块

- **命名约定**：查找类似 `core`、`service`、`utils`、`common`、`api`、`client` 的命名模式。
- **依赖方向**：工具模块通常被许多其他模块依赖，但其自身对外依赖较少。核心业务模块可能有更复杂的相互依赖关系。
- **功能性**：检查模块内类的主要用途以确定其角色。
