---
description: 
globs: 
alwaysApply: false
---
# Java Monorepo：技术栈识别指南

本指南旨在协助识别 Java monorepo 中使用的关键技术。

## 1. 构建工具确认

- 虽然项目假定基于 Maven（[pom.xml](mdc:pom.xml)），但请确认是否存在 Ant (`build.xml`) 或 Gradle (`build.gradle`, `settings.gradle`) 文件，这可能表明存在混合构建系统或遗留部分。

## 2. 识别关键框架和库

重点关注 `[pom.xml](mdc:pom.xml)` 文件（或等效的构建文件）中声明的依赖项。

- **Spring Framework / Spring Boot**：
    - 查找类似 `spring-core`、`spring-context`、`spring-boot-starter`、`spring-boot-starter-web` 的依赖。
    - 检查常见的 Spring 注解：`@SpringBootApplication`、`@RestController`、`@Service`、`@Component`、`@Configuration`。
    - 配置文件：通常在 `src/main/resources` 中可以找到 `application.properties`、`application.yml`。

- **ORM 框架**（例如，Hibernate、MyBatis）：
    - 依赖项：`hibernate-core`、`spring-boot-starter-data-jpa`、`mybatis`、`mybatis-spring-boot-starter`。
    - 配置：`hibernate.cfg.xml`、`persistence.xml` 或 Spring Boot 自动配置属性。
    - 实体类：使用 `@Entity` (JPA/Hibernate) 注解或 Mapper XML 文件/接口 (MyBatis)。

- **日志组件**（例如，Log4j、SLF4J、Logback）：
    - 依赖项：`slf4j-api`、`logback-classic`、`log4j`。
    - 配置：`src/main/resources` 中的 `logback.xml`、`log4j.properties`、`log4j2.xml`。

- **配置管理**：
    - 如果使用了专门的配置中心（例如，Spring Cloud Config、Apollo、Nacos），请查找相应的客户端依赖。
    - 属性文件（`.properties`、`.yml`）可能包含连接到此类中心的详细信息。

- **消息队列**（例如 RabbitMQ、Kafka）：
    - 依赖项：`spring-rabbit`、`spring-kafka`、`kafka-clients`。
    - 与 Broker 和 Topic 相关的配置。

- **缓存**（例如 Redis、EhCache、Caffeine）：
    - 依赖项：`spring-boot-starter-data-redis`、`ehcache`、`caffeine`。

## 3. 识别安全相关库

- **Spring Security**：`spring-boot-starter-security`、`spring-security-oauth2-client`。
- **其他安全库**：查找与 JWT、OAuth、加密（例如，Bouncy Castle）相关的依赖项。

## 4. 数据库连接

- JDBC 驱动程序（例如，`mysql-connector-java`、`postgresql`）。
- 数据库连接池库（例如，HikariCP、C3P0、DBCP）。
- 数据库 URL、用户名和密码的配置详细信息（通常在 `application.properties` 或 `application.yml` 中）。

## 5. 前端技术（如果适用）

- 如果任何模块提供 UI，请检查 `package.json` (Node.js)，或类似 `src/main/webapp`、`static`、`templates` 的目录，这些目录可能包含 HTML、CSS、JavaScript 或模板引擎文件（例如，Thymeleaf、JSP）。

## 工作流程说明：

- 优先分析父 POM 和通用/核心模块，因为它们通常定义或管理广泛使用的技术的版本。
