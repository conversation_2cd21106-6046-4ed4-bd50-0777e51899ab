# 检测操作系统类型
UNAME_S := $(shell uname -s)

# 根据操作系统选择 docker-compose 文件
ifeq ($(UNAME_S),Darwin)
    DOCKER_COMPOSE_FILE := docker-compose.mac.yml
	DOCKER_BUILD_FILE := d42paas_manager/Dockerfile-mac
else
    DOCKER_COMPOSE_FILE := docker-compose.yml
	DOCKER_BUILD_FILE := d42paas_manager/Dockerfile
endif

# 从配置文件读取数据库配置
DB_NAME := $(shell grep -A 5 "datasource:" d42paas_manager/src/main/resources/application-local.yml | grep "url:" | sed 's/.*\/\([^?]*\).*/\1/')
DB_USER := $(shell grep -A 5 "datasource:" d42paas_manager/src/main/resources/application-local.yml | grep "username:" | awk '{print $$2}')
DB_PASS := $(shell grep -A 5 "datasource:" d42paas_manager/src/main/resources/application-local.yml | grep "password:" | awk '{print $$2}')

# 从配置文件读取 Redis 配置
REDIS_PASS := $(shell grep -A 5 "redis:" d42paas_manager/src/main/resources/application-local.yml | grep "password:" | awk '{print $$2}')

# 从配置文件读取 RabbitMQ 配置
RABBITMQ_USER := $(shell grep -A 5 "rabbitmq:" d42paas_manager/src/main/resources/application-local.yml | grep "username:" | awk '{print $$2}')
RABBITMQ_PASS := $(shell grep -A 5 "rabbitmq:" d42paas_manager/src/main/resources/application-local.yml | grep "password:" | awk '{print $$2}')
RABBITMQ_VHOST := $(shell grep -A 5 "rabbitmq:" d42paas_manager/src/main/resources/application-local.yml | grep "virtual-host:" | awk '{print $$2}')

# 定义成功和失败的图标
SUCCESS_ICON := ✅
FAIL_ICON := ❌

.PHONY: local test init build db middleware start clean

# 清理已存在的 manager-app 容器
clean:
	@echo "检查并清理已存在的 manager-app 容器..."
	@if docker ps -a | grep -q manager-app; then \
		echo "发现已存在的 manager-app 容器，正在停止并删除..." && \
		docker stop manager-app > /dev/null 2>&1 && \
		docker rm manager-app > /dev/null 2>&1 && \
		echo "$(SUCCESS_ICON) 容器清理完成"; \
	else \
		echo "$(SUCCESS_ICON) 没有发现已存在的 manager-app 容器"; \
	fi

# 本地环境搭建
middleware:
	@echo "正在使用 $(DOCKER_COMPOSE_FILE) 启动中间件服务..."
	docker-compose -f $(DOCKER_COMPOSE_FILE) up -d
	@echo "等待中间件服务启动..."
	@sleep 10
db:
	@echo "检查MYSQL数据库是否存在..."
	@if docker-compose -f $(DOCKER_COMPOSE_FILE) exec -T mysql bash -c 'MYSQL_PWD=$(DB_PASS) mysql -u$(DB_USER) -e "SHOW DATABASES LIKE \"$(DB_NAME)\""' | grep -q "$(DB_NAME)"; then \
		echo "数据库存在，检查表结构..." && \
		if docker-compose -f $(DOCKER_COMPOSE_FILE) exec -T mysql bash -c 'MYSQL_PWD=$(DB_PASS) mysql -u$(DB_USER) -e "USE $(DB_NAME); SHOW TABLES"' | grep -q -v "Tables_in_$(DB_NAME)"; then \
			echo "$(SUCCESS_ICON) 数据库已存在且至少有一个表，跳过初始化"; \
		else \
			echo "数据库存在但没有表，开始初始化..." && \
			docker-compose -f $(DOCKER_COMPOSE_FILE) exec -T mysql bash -c 'MYSQL_PWD=$(DB_PASS) mysql -u$(DB_USER) $(DB_NAME) < /docker-entrypoint-initdb.d/paas_develop.schema.sql' && \
			echo "$(SUCCESS_ICON) 数据库初始化完成"; \
		fi; \
	else \
		echo "数据库不存在，开始初始化..." && \
		docker-compose -f $(DOCKER_COMPOSE_FILE) exec -T mysql bash -c 'MYSQL_PWD=$(DB_PASS) mysql -u$(DB_USER) -e "CREATE DATABASE IF NOT EXISTS $(DB_NAME) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"' && \
		docker-compose -f $(DOCKER_COMPOSE_FILE) exec -T mysql bash -c 'MYSQL_PWD=$(DB_PASS) mysql -u$(DB_USER) $(DB_NAME) < /docker-entrypoint-initdb.d/paas_develop.schema.sql' && \
		echo "$(SUCCESS_ICON) 数据库初始化完成"; \
	fi
	@echo "$(SUCCESS_ICON) 本地环境启动完成"

stop:
	@echo "停止 Manager 服务..."
	-docker stop manager-app > /dev/null 2>&1
	@echo "$(SUCCESS_ICON) Manager 服务停止完成"
	@echo "停止中间件服务..."
	@docker-compose -f $(DOCKER_COMPOSE_FILE) down > /dev/null 2>&1
	@echo "$(SUCCESS_ICON) 中间件服务停止完成"


build:clean
	@echo "正在构建 Manager 服务..."
	DOCKER_BUILDKIT=1 docker build -t manager-app -f $(DOCKER_BUILD_FILE) .
	@echo "$(SUCCESS_ICON) Manager 服务构建完成"

start: clean
	@echo "正在启动中间件服务..."
	@docker-compose -f $(DOCKER_COMPOSE_FILE) up -d > /dev/null 2>&1
	@echo "正在启动 Manager 服务..."
	docker run -d --name manager-app --add-host=host.docker.internal:host-gateway -p 8000:8000 manager-app
	@echo "$(SUCCESS_ICON) Manager 服务启动完成"

# 测试环境
test:
	@echo "开始测试环境..."
	@echo "测试 MySQL 连接..."
	@docker-compose -f $(DOCKER_COMPOSE_FILE) exec -T mysql bash -c 'MYSQL_PWD=$(DB_PASS) mysql -u$(DB_USER) -e "SELECT 1"' > /dev/null 2>&1 && echo "$(SUCCESS_ICON) MySQL 连接正常" || echo "$(FAIL_ICON) MySQL 连接失败"
	
	@echo "测试 Redis 连接..."
	@docker-compose -f $(DOCKER_COMPOSE_FILE) exec -T redis redis-cli -a $(REDIS_PASS) ping > /dev/null 2>&1 && echo "$(SUCCESS_ICON) Redis 连接正常" || echo "$(FAIL_ICON) Redis 连接失败"
	
	@echo "测试 RabbitMQ 连接..."
	@docker-compose -f $(DOCKER_COMPOSE_FILE) exec -T rabbitmq rabbitmqctl status > /dev/null 2>&1 && echo "$(SUCCESS_ICON) RabbitMQ 连接正常" || echo "$(FAIL_ICON) RabbitMQ 连接失败"
	
	@echo "测试 Manager 服务..."
	@curl -s -o /dev/null -w "%{http_code}" --location --request POST 'http://127.0.0.1:8000/api/v1/sdk/ticket' \
		--header 'Host: www.clackypaas.com' \
		--header 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
		--header 'Accept: application/json, text/plain, */*' \
		--header 'Accept-Encoding: deflate, gzip,tr,zstd' \
		--header 'Accept-Language: zh-CN,zh;q=0.9' \
		--header 'Authorization: Bearer test' \
		--header 'Cache-Control: no-cache' \
		--header 'Content-Type: application/json' \
		--header 'Cookie: _ga=GA1.1.257457897.1738814395;' \
		--header 'Origin: https://app.clacky.ai' \
		--header 'Pragma: no-cache' \
		--header 'Referer: https://app.clacky.ai/thread/0196fc15-d679-72f2-bdf2-659483250ef3' \
		--header 'Tenantcode: demo' \
		--data '{"playgroundId":"748675794851037184","tillTime":0,"userInfo":{"name":"yunnanwenshan","userId":"user_2qHZCVBLbSbm8RuJhoApp7CKlg7"},"tenantCode":"demo"}' | grep -q "^[0-9]" && echo "$(SUCCESS_ICON) Manager 服务正常" || echo "$(FAIL_ICON) Manager 服务异常"

init:
	make middleware
	make db
	make build
	make start
	@echo "等待 Manager 服务启动..."
	@sleep 10
	make test