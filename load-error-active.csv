﻿时间,time_us,Pod 名称,完整信息
2025-06-12T18:16:46+08:00,**********798829,paas-manager-staging-7c449c6d95-tldsw,"2025-06-12 18:16:46,664 [http-nio-8000-exec-27] [DEBUG] [MQMessageSender] [trace_id]=26932f1ffdb93ab60d991bf2fd5342f3 span_id=3cedf1b0f85ea4b8 trace_flags=01 [PlaygroundId]= [DockerId]= - RoutingKey: toDocker.804877517583753216.804877517617307648, Type: changeIdeServer, Content: {""messageId"":""3841bbef-3f2e-420f-8189-8d1dc144ec6f"",""timestamp"":**********,""ideServerCode"":""paas-ide-server-749ccbc465-zn9cd""}"
2025-06-12T18:16:47+08:00,**********191519,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:16:46,666 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-64] [DEBUG] [AlterMQReceiver] [trace_id]=26932f1ffdb93ab60d991bf2fd5342f3 span_id=351a80a51fc3486f trace_flags=01 [PlaygroundId]=804877422331109376 [DockerId]=804877422473715712 - AlterMQMsg:(Body:'[B@13357b31(byte[126])' MessageProperties [headers={traceparent=00-26932f1ffdb93ab60d991bf2fd5342f3-a2e55b54e7fbb13a-01}, type=changeIdeServer, contentType=application/octet-stream, contentLength=0, receivedDeliveryMode=PERSISTENT, priority=9, redelivered=false, receivedExchange=paas, receivedRoutingKey=toDocker.804877517583753216.804877517617307648, deliveryTag=81, consumerTag=amq.ctag-TywkCVpDtbSMtHc_Vwa0pg, consumerQueue=exception])"
2025-06-12T18:16:47+08:00,**********191908,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:16:47,107 [paas-active-314] [DEBUG] [MQMessageSender] [trace_id]=f4525446dbcfd3c6586d2dbe019be4ae span_id=5c3f05b84068e337 trace_flags=01 [PlaygroundId]=804877517583753216 [DockerId]= - RoutingKey: toDocker.804877517583753216.804877517617307648, Type: changeIdeServer, Content: {""messageId"":""37a00c52-ea7d-4511-b755-3c8860d0e74e"",""timestamp"":**********,""ideServerCode"":""paas-ide-server-785b85dffd-zrn4h""}"
2025-06-12T18:16:47+08:00,**********191909,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:16:47,109 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-72] [DEBUG] [AlterMQReceiver] [trace_id]=f4525446dbcfd3c6586d2dbe019be4ae span_id=e4384ccdde33d37f trace_flags=01 [PlaygroundId]=804877422486020096 [DockerId]=804877422561517569 - AlterMQMsg:(Body:'[B@5c18c9e(byte[126])' MessageProperties [headers={traceparent=00-f4525446dbcfd3c6586d2dbe019be4ae-504d8a95a3d33f24-01}, type=changeIdeServer, contentType=application/octet-stream, contentLength=0, receivedDeliveryMode=PERSISTENT, priority=9, redelivered=false, receivedExchange=paas, receivedRoutingKey=toDocker.804877517583753216.804877517617307648, deliveryTag=81, consumerTag=amq.ctag-yu_D3UgqCrOKRIQLITuUqw, consumerQueue=exception])"
2025-06-12T18:16:47+08:00,**********793475,paas-ide-server-785b85dffd-zrn4h,"[Nest] 44  - 06/12/2025, 6:16:47 PM    INFO [RabbitmqService] [mqName:paas-ide-server-785b85dffd-zrn4h][playgroundId:804877517583753216][rabbitmq.service.ts:129] <=[fromMQ] playgroundInfo[804877517583753216]:{""messageId"":""bd66c89e-5695-4fb2-9a27-c3456879054d"",""timestamp"":**********,""replyMessageId"":"""",""status"":""INACTIVE"",""dockerId"":""804877517617307648"",""fileRootPath"":""/app/data/codeZone/2025/1/6-10/@4047617b-ef8b-4064-9bb3-8f26b67d92e9/dependency/home/<USER>"",""fileRootId"":""home"",""lspRootPath"":""/home/<USER>/app"",""fileTreeIgnore"":"".git/;.1024.nix;.1024feature*;.nfs*;*.dll;*.swp;.paas-unit-*;core.*;.breakpoints;.idea/;.vscode/;.1024feature-file;.pnpm-store/;node_modules/"",""fileTreePackage"":""go.mod"",""url"":"""",""lspLanguageId"":""Go"",""language"":""Go"",""lspSupported"":true,""gui"":false,""debugSupport"":false,""debugState"":""stop"",""defaultOpenFile"":"""",""refresh"":true,""realTimeRefresh"":false,""intervalTime"":0,""environmentVersion"":{""id"":403710021818515460,""envCode"":""code_go_1_23"",""name"":""Go 1.23"",""runtime"":""go1.23 and node v20.1.0 already installed, based on Ubuntu 22.04.4"",""packageManagers"":[""go 1.23"",""npm 9.6.4""],""language"":""Go"",""runtimeInformation"":[""bash 5.1"",""git 2.34.1"",""mysql-client-8.0 8.0.40"",""mongodb-mongosh 2.3.8"",""postgresql-client-14 14.15"",""redis-tools 6.0.16"",""sqlite3 3.37.2""],""tags"":[""Pure Language""]},""vncSupport"":false} +5ms"
2025-06-12T18:16:47+08:00,**********793476,paas-ide-server-785b85dffd-zrn4h,"[Nest] 44  - 06/12/2025, 6:16:47 PM   DEBUG [PlaygroundRabbitMQ] [mqName:paas-ide-server-785b85dffd-zrn4h][agentUserId:4313dfe8-4c7c-4be4-bd5c-7b33b82743c9][playgroundId:804877517583753216][playgroundRabbitMQ.ts:854] >>> _receivePlaygroundInfoEvent[804877517583753216], data: {""messageId"":""bd66c89e-5695-4fb2-9a27-c3456879054d"",""timestamp"":**********,""replyMessageId"":"""",""status"":""INACTIVE"",""dockerId"":""804877517617307648"",""fileRootPath"":""/app/data/codeZone/2025/1/6-10/@4047617b-ef8b-4064-9bb3-8f26b67d92e9/dependency/home/<USER>"",""fileRootId"":""home"",""lspRootPath"":""/home/<USER>/app"",""fileTreeIgnore"":"".git/;.1024.nix;.1024feature*;.nfs*;*.dll;*.swp;.paas-unit-*;core.*;.breakpoints;.idea/;.vscode/;.1024feature-file;.pnpm-store/;node_modules/"",""fileTreePackage"":""go.mod"",""url"":"""",""lspLanguageId"":""Go"",""language"":""Go"",""lspSupported"":true,""gui"":false,""debugSupport"":false,""debugState"":""stop"",""defaultOpenFile"":"""",""refresh"":true,""realTimeRefresh"":false,""intervalTime"":0,""environmentVersion"":{""id"":403710021818515460,""envCode"":""code_go_1_23"",""name"":""Go 1.23"",""runtime"":""go1.23 and node v20.1.0 already installed, based on Ubuntu 22.04.4"",""packageManagers"":[""go 1.23"",""npm 9.6.4""],""language"":""Go"",""runtimeInformation"":[""bash 5.1"",""git 2.34.1"",""mysql-client-8.0 8.0.40"",""mongodb-mongosh 2.3.8"",""postgresql-client-14 14.15"",""redis-tools 6.0.16"",""sqlite3 3.37.2""],""tags"":[""Pure Language""]},""vncSupport"":false} +0ms"
2025-06-12T18:16:48+08:00,****************,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:16:47,110 [paas-active-314] [DEBUG] [MQMessageSender] [trace_id]=f4525446dbcfd3c6586d2dbe019be4ae span_id=5c3f05b84068e337 trace_flags=01 [PlaygroundId]=804877517583753216 [DockerId]= - RoutingKey: paas-ide-server-785b85dffd-zrn4h.fromPlayground.804877517583753216, Type: playgroundInfo, Content: {""messageId"":""bd66c89e-5695-4fb2-9a27-c3456879054d"",""timestamp"":**********,""replyMessageId"":"""",""status"":""INACTIVE"",""dockerId"":""804877517617307648"",""fileRootPath"":""/app/data/codeZone/2025/1/6-10/@4047617b-ef8b-4064-9bb3-8f26b67d92e9/dependency/home/<USER>"",""fileRootId"":""home"",""lspRootPath"":""/home/<USER>/app"",""fileTreeIgnore"":"".git/;.1024.nix;.1024feature*;.nfs*;*.dll;*.swp;.paas-unit-*;core.*;.breakpoints;.idea/;.vscode/;.1024feature-file;.pnpm-store/;node_modules/"",""fileTreePackage"":""go.mod"",""url"":"""",""lspLanguageId"":""Go"",""language"":""Go"",""lspSupported"":true,""gui"":false,""debugSupport"":false,""debugState"":""stop"",""defaultOpenFile"":"""",""refresh"":true,""realTimeRefresh"":false,""intervalTime"":0,""environmentVersion"":{""id"":403710021818515464,""envCode"":""code_go_1_23"",""name"":""Go 1.23"",""runtime"":""go1.23 and node v20.1.0 already installed, based on Ubuntu 22.04.4"",""packageManagers"":[""go 1.23"",""npm 9.6.4""],""language"":""Go"",""runtimeInformation"":[""bash 5.1"",""git 2.34.1"",""mysql-client-8.0 8.0.40"",""mongodb-mongosh 2.3.8"",""postgresql-client-14 14.15"",""redis-tools 6.0.16"",""sqlite3 3.37.2""],""tags"":[""Pure Language""]},""vncSupport"":false}"
2025-06-12T18:16:55+08:00,1749723415882616,paas-ide-server-785b85dffd-zrn4h,"[Nest] 44  - 06/12/2025, 6:16:55 PM   DEBUG [PlaygroundItem] [mqName:paas-ide-server-785b85dffd-zrn4h][agentUserId:4313dfe8-4c7c-4be4-bd5c-7b33b82743c9][playgroundId:804877517583753216][playgroundItem.ts:513] [consumerLayer][804877517617307648]:{""playgroundItemInfo"":{""messageId"":""bd66c89e-5695-4fb2-9a27-c3456879054d"",""timestamp"":**********,""replyMessageId"":"""",""status"":""INACTIVE"",""dockerId"":""804877517617307648"",""fileRootPath"":""/app/data/codeZone/2025/1/6-10/@4047617b-ef8b-4064-9bb3-8f26b67d92e9/dependency/home/<USER>"",""fileRootId"":""home"",""lspRootPath"":""/home/<USER>/app"",""fileTreeIgnore"":"".git/;.1024.nix;.1024feature*;.nfs*;*.dll;*.swp;.paas-unit-*;core.*;.breakpoints;.idea/;.vscode/;.1024feature-file;.pnpm-store/;node_modules/"",""fileTreePackage"":""go.mod"",""url"":"""",""lspLanguageId"":""Go"",""language"":""Go"",""lspSupported"":true,""gui"":false,""debugSupport"":false,""debugState"":""stop"",""defaultOpenFile"":"""",""refresh"":true,""realTimeRefresh"":false,""intervalTime"":0,""environmentVersion"":{""id"":403710021818515460,""envCode"":""code_go_1_23"",""name"":""Go 1.23"",""runtime"":""go1.23 and node v20.1.0 already installed, based on Ubuntu 22.04.4"",""packageManagers"":[""go 1.23"",""npm 9.6.4""],""language"":""Go"",""runtimeInformation"":[""bash 5.1"",""git 2.34.1"",""mysql-client-8.0 8.0.40"",""mongodb-mongosh 2.3.8"",""postgresql-client-14 14.15"",""redis-tools 6.0.16"",""sqlite3 3.37.2""],""tags"":[""Pure Language""]},""vncSupport"":false,""lspStatus"":""uninitialized""},""fileTree"":{""type"":""DIRECTORY"",""name"":""."",""path"":""."",""children"":[{""type"":""FILE"",""name"":"".1024"",""path"":"".1024"",""children"":[],""hide"":false,""lock"":false,""unittest"":false,""isRetainedFile"":false},{""type"":""FILE"",""name"":"".1024nix"",""path"":"".1024nix"",""children"":[],""hide"":false,""lock"":false,""unittest"":false,""isRetainedFile"":false},{""type"":""FILE"",""name"":"".gitignore"",""path"":"".gitignore"",""children"":[],""hide"":false,""lock"":false,""unittest"":false,""isRetainedFile"":false},{""type"":""FILE"",""name"":""README.md"",""path"":""README.md"",""children"":[],""hide"":false,""lock"":false,""unittest"":false,""isRetainedFile"":false},{""type"":""FILE"",""name"":""main.go"",""path"":""main.go"",""children"":[],""hide"":false,""lock"":false,""unittest"":false,""isRetainedFile"":false}]}} +0ms"
2025-06-12T18:17:12+08:00,1749723432227343,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:17:12,055 [paas-async-60] [INFO ] [PlaygroundService] [trace_id]=1a59c6b59d87b6f9f6d362e265a54151 span_id=e56a99c2903743c2 trace_flags=01 [PlaygroundId]=804877517583753216 [DockerId]=804877517617307648 - DockerScheduling-Active, active start, playgroundId: 804877517583753216,dockerId: 804877517617307648,docker status: NOT_INIT"
2025-06-12T18:17:12+08:00,1749723432227344,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:17:12,060 [paas-async-60] [DEBUG] [MemoryDockerServerSelector] [trace_id]=1a59c6b59d87b6f9f6d362e265a54151 span_id=100609964a8e26c9 trace_flags=01 [PlaygroundId]=804877517583753216 [DockerId]=804877517617307648 - DockerScheduling-Active, Request resource.ServerId:804875731661692928, memory:4096, cpu load: 2"
2025-06-12T18:17:12+08:00,1749723432227345,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:17:12,076 [paas-async-60] [DEBUG] [MemoryDockerServerSelector] [trace_id]=1a59c6b59d87b6f9f6d362e265a54151 span_id=100609964a8e26c9 trace_flags=01 [PlaygroundId]=804877517583753216 [DockerId]=804877517617307648 - DockerScheduling-Active, Memory request on server(804875731661692928) succeed, left memory: 4096, cpu load: 2, memoryAfterRequest: 211352.0, cpuLoadAfterRequest: 12.0"
2025-06-12T18:17:12+08:00,1749723432227346,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:17:12,082 [paas-async-60] [INFO ] [DockerService] [trace_id]=1a59c6b59d87b6f9f6d362e265a54151 span_id=e56a99c2903743c2 trace_flags=01 [PlaygroundId]=804877517583753216 [DockerId]=804877517617307648 - generateConfig, playgroundId:804877517583753216, dockerId:804877517617307648, environmentVerId:403710021818515464"
2025-06-12T18:17:12+08:00,1749723432227347,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:17:12,084 [paas-async-60] [INFO ] [URLResourceService] [trace_id]=1a59c6b59d87b6f9f6d362e265a54151 span_id=e56a99c2903743c2 trace_flags=01 [PlaygroundId]=804877517583753216 [DockerId]=804877517617307648 - DockerScheduling-Active, bindTempURLResource, dockerId:804877517617307648, type: SSH_URL_RESOURCE"
2025-06-12T18:17:12+08:00,1749723432227348,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:17:12,103 [paas-async-60] [DEBUG] [URLResourceService] [trace_id]=1a59c6b59d87b6f9f6d362e265a54151 span_id=e56a99c2903743c2 trace_flags=01 [PlaygroundId]=804877517583753216 [DockerId]=804877517617307648 - DockerScheduling-Active, bindTempURLResource,insert dockerId:804877517617307648, type: SSH_URL_RESOURCE, DockerURLResourceID: 804877626262085632"
2025-06-12T18:17:12+08:00,1749723432227349,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:17:12,104 [paas-async-60] [INFO ] [URLResourceService] [trace_id]=1a59c6b59d87b6f9f6d362e265a54151 span_id=e56a99c2903743c2 trace_flags=01 [PlaygroundId]=804877517583753216 [DockerId]=804877517617307648 - DockerScheduling-Active, bindTempURLResource, dockerId:804877517617307648, type: SERVICE_URL_RESOURCE"
2025-06-12T18:17:12+08:00,1749723432227681,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:17:12,126 [paas-async-60] [DEBUG] [URLResourceService] [trace_id]=1a59c6b59d87b6f9f6d362e265a54151 span_id=e56a99c2903743c2 trace_flags=01 [PlaygroundId]=804877517583753216 [DockerId]=804877517617307648 - DockerScheduling-Active, bindTempURLResource,insert dockerId:804877517617307648, type: SERVICE_URL_RESOURCE, DockerURLResourceID: 804877626358554624"
2025-06-12T18:17:12+08:00,1749723432227682,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:17:12,127 [paas-async-60] [INFO ] [URLResourceService] [trace_id]=1a59c6b59d87b6f9f6d362e265a54151 span_id=e56a99c2903743c2 trace_flags=01 [PlaygroundId]=804877517583753216 [DockerId]=804877517617307648 - DockerScheduling-Active, bindTempURLResource, dockerId:804877517617307648, type: LSP_URL_RESOURCE"
2025-06-12T18:17:12+08:00,1749723432227683,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:17:12,146 [paas-async-60] [DEBUG] [URLResourceService] [trace_id]=1a59c6b59d87b6f9f6d362e265a54151 span_id=e56a99c2903743c2 trace_flags=01 [PlaygroundId]=804877517583753216 [DockerId]=804877517617307648 - DockerScheduling-Active, bindTempURLResource,insert dockerId:804877517617307648, type: LSP_URL_RESOURCE, DockerURLResourceID: 804877626442440704"
2025-06-12T18:17:12+08:00,1749723432227684,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:17:12,146 [paas-async-60] [INFO ] [URLResourceService] [trace_id]=1a59c6b59d87b6f9f6d362e265a54151 span_id=e56a99c2903743c2 trace_flags=01 [PlaygroundId]=804877517583753216 [DockerId]=804877517617307648 - DockerScheduling-Active, bindTempURLResource, dockerId:804877517617307648, type: AGENT_SERVER_RESOURCE"
2025-06-12T18:17:12+08:00,1749723432227685,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:17:12,166 [paas-async-60] [DEBUG] [URLResourceService] [trace_id]=1a59c6b59d87b6f9f6d362e265a54151 span_id=e56a99c2903743c2 trace_flags=01 [PlaygroundId]=804877517583753216 [DockerId]=804877517617307648 - DockerScheduling-Active, bindTempURLResource,insert dockerId:804877517617307648, type: AGENT_SERVER_RESOURCE, DockerURLResourceID: 804877626526326784"
2025-06-12T18:17:12+08:00,1749723432227853,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:17:12,166 [paas-async-60] [DEBUG] [DockerExternalService] [trace_id]=1a59c6b59d87b6f9f6d362e265a54151 span_id=e56a99c2903743c2 trace_flags=01 [PlaygroundId]=804877517583753216 [DockerId]=804877517617307648 - DockerScheduling-Active begin, 创建go agent主容器, docker_id:804877517617307648; docker_server_id:804875731661692928"
2025-06-12T18:17:13+08:00,1749723433228665,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:17:12,171 [paas-async-60] [DEBUG] [DockerExternalService] [trace_id]=1a59c6b59d87b6f9f6d362e265a54151 span_id=e56a99c2903743c2 trace_flags=01 [PlaygroundId]=804877517583753216 [DockerId]=804877517617307648 - DockerScheduling-Active, Create container, image: ghcr.io/dao42/app/docker:develop-20250516, hostConfig: HostConfig(binds=[/app/agent:/agent:ro, /app/data/codeZone/2025/1/6-10/@4047617b-ef8b-4064-9bb3-8f26b67d92e9/dependency/home:/home/<USER>/:rw, /app/data/codeZone/2025/1/6-10/@4047617b-ef8b-4064-9bb3-8f26b67d92e9/dependency/.gvm:/home/<USER>/.gvm:rw, /app/data/codeZone/2025/1/6-10/@4047617b-ef8b-4064-9bb3-8f26b67d92e9/dependency/apt/etc:/etc:rw, /app/data/codeZone/2025/1/6-10/@4047617b-ef8b-4064-9bb3-8f26b67d92e9/dependency/apt/var:/var:rw, /app/data/codeZone/2025/1/6-10/@4047617b-ef8b-4064-9bb3-8f26b67d92e9/dependency/apt/usr:/usr:rw], blkioWeight=null, blkioWeightDevice=null, blkioDeviceReadBps=[BlkioRateDevice(path=/dev/nvme0n1, rate=41943040)], blkioDeviceWriteBps=[BlkioRateDevice(path=/dev/nvme0n1, rate=41943040)], blkioDeviceReadIOps=[BlkioRateDevice(path=/dev/nvme0n1, rate=1000)], blkioDeviceWriteIOps=[BlkioRateDevice(path=/dev/nvme0n1, rate=1000)], memorySwappiness=null, nanoCPUs=null, capAdd=null, capDrop=null, containerIDFile=null, cpuPeriod=null, cpuRealtimePeriod=null, cpuRealtimeRuntime=null, cpuShares=1024, cpuQuota=null, cpusetCpus=null, cpusetMems=null, devices=null, deviceCgroupRules=null, deviceRequests=null, diskQuota=null, dns=null, dnsOptions=null, dnsSearch=null, extraHosts=null, groupAdd=null, ipcMode=null, cgroup=null, links=[], logConfig=LogConfig(type=json-file, config={max-size=10m}), lxcConf=null, memory=4294967296, memorySwap=4294967296, memoryReservation=4294967296, kernelMemory=null, networkMode=null, oomKillDisable=true, init=null, autoRemove=null, oomScoreAdj=null, portBindings={22/tcp=[Lcom.github.dockerjava.api.model.Ports$Binding;@2722caba, 10087/tcp=[Lcom.github.dockerjava.api.model.Ports$Binding;@531487bd, 10086/tcp=[Lcom.github.dockerjava.api.model.Ports$Binding;@5adf3967, 8080/tcp=[Lcom.github.dockerjava.api.model.Ports$Binding;@3f1a1322, 45678/tcp=[Lcom.github.dockerjava.api.model.Ports$Binding;@edc7961}, privileged=null, publishAllPorts=null, readonlyRootfs=null, restartPolicy=null, ulimits=null, cpuCount=null, cpuPercent=null, ioMaximumIOps=null, ioMaximumBandwidth=null, volumesFrom=null, mounts=null, pidMode=null, isolation=null, securityOpts=null, storageOpt=null, cgroupParent=null, volumeDriver=null, shmSize=null, pidsLimit=null, runtime=null, tmpFs=null, utSMode=null, usernsMode=null, sysctls=null, consoleSize=null, cgroupnsMode=null), containerName: dev-804877517617307648, envList: [paas_docker_id=804877517617307648, paas_playground_id=804877517583753216, paas_ide_server_code=paas-ide-server-785b85dffd-zrn4h, paas_mq_host=rabbitmq, paas_mq_port=5672, paas_exchange_name=paas, paas_mq_virtual_host=paas, paas_app_path=/home/<USER>/app, paas_file_tree_ignore=.git/;.1024.nix;.1024feature*;.nfs*;*.dll;*.swp;.paas-unit-*;core.*;.breakpoints;.idea/;.vscode/;.1024feature-file;.pnpm-store/;node_modules/, paas_shell_cmd=bash, paas_shell_cmd_type=bash, paas_inactive_seconds=194, paas_language_bash_cmd=gvm use go1.23 --default, paas_language=Go, paas_language_package=gvm, paas_service_port=8080, paas_project_web_port=10086, paas_agent_server_port=10087, paas_agent_server_url=dbb0d02f4a143e93e9073de50c3cfa08-agentserver.staging.clackypaas.com, paas_url=0f144bb13918bf49c13a1a22c1f6c0e3-app.staging.clackypaas.com, paas_lsp_language_id=Go, paas_lsp_language_ids=Go,Typescript, paas_lsp_port=45678, paas_lsp_start_cmd=gopls -mode=stdio, paas_lsp_start_cmds=Go,gopls -mode=stdio#TypeScript,typescript-language-server --stdio, paas_debug_start_cmd=dlv dap --check-go-version=false --listen=127.0.0.1:34567, paas_lsp_url=fabc1f0ae4b943c0d6e11ac7e93d16a4-lsp.staging.clackypaas.com, paas_ssh_url=3599392452c394850ddcb6d141798381ssh.staging.clackypaas.com, paas_rag_path=/home/<USER>/.rag, paas_common_id=claky_encryptKey, paas_resource_id=3060e0d40a91f77a3c53d3a612d41b87834933335ded65708adb18d1518df170327cb10643d6271684ab6259308e6eb3f1f678979a68fae3b1f96353f9579800da798c4fc17e25df41f0e4ab8ddeb44d8ae2071bcb01b684c0997a57ec8bb62c6b782cbbb6889b79207baaaabb9eeea785b05ed494886373bb75932c4cc2e4ea93429d82ec4d279ba9bf4e5ddce341fcc47d6e7d3bb7d8cdcf472577a76af1dd339bdce6ce20fdd3dd9d38542e2f53d4, paas_base_url=5c66f3ad93b8327cf5fd5848e34f611dcd9591aed83e62cb9d155b0621f12a5f, paas_auth_token=dbe051be7bde12c2ce40aed567722441c92eef091bb26a895a0b948cc1ec7aab, paas_gateway_url=https://paas-gateway.staging.clackypaas.com, paas_config_file_name=.1024, paas_resource_monitoring=true, paas_unittest_framework_code=]"
2025-06-12T18:17:13+08:00,1749723433228670,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:17:12,685 [paas-async-60] [INFO ] [DockerService] [trace_id]=1a59c6b59d87b6f9f6d362e265a54151 span_id=e56a99c2903743c2 trace_flags=01 [PlaygroundId]=804877517583753216 [DockerId]=804877517617307648 - DockerScheduling-Active, 创建容器;id:804877517617307648;dockerServiceId:804875731661692928, time: 519 ms"
2025-06-12T18:17:13+08:00,1749723433228671,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:17:12,700 [paas-async-60] [INFO ] [DockerService] [trace_id]=1a59c6b59d87b6f9f6d362e265a54151 span_id=e56a99c2903743c2 trace_flags=01 [PlaygroundId]= [DockerId]=804877517617307648 - DockerScheduling-Active, startContainer-start, id: 804877517617307648"
2025-06-12T18:17:13+08:00,1749723433228672,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:17:12,700 [paas-async-60] [DEBUG] [RedisExternalService] [trace_id]=1a59c6b59d87b6f9f6d362e265a54151 span_id=e56a99c2903743c2 trace_flags=01 [PlaygroundId]= [DockerId]=804877517617307648 - redis >>> set mq:waitDockerInfo:804877517617307648"
2025-06-12T18:17:13+08:00,1749723433228673,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:17:12,700 [paas-async-60] [DEBUG] [RedisExternalService] [trace_id]=1a59c6b59d87b6f9f6d362e265a54151 span_id=e56a99c2903743c2 trace_flags=01 [PlaygroundId]= [DockerId]=804877517617307648 - add redis key:mq:waitDockerInfo:804877517617307648,value:804877517617307648"
2025-06-12T18:17:13+08:00,1749723433228674,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:17:12,700 [paas-async-60] [DEBUG] [RedisExternalService] [trace_id]=1a59c6b59d87b6f9f6d362e265a54151 span_id=e56a99c2903743c2 trace_flags=01 [PlaygroundId]= [DockerId]=804877517617307648 - hasMillis:false"
2025-06-12T18:17:13+08:00,1749723433228675,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:17:12,706 [paas-async-60] [INFO ] [DockerExternalService] [trace_id]=1a59c6b59d87b6f9f6d362e265a54151 span_id=e56a99c2903743c2 trace_flags=01 [PlaygroundId]= [DockerId]=804877517617307648 - DockerScheduling-Active, 启动容器:804877517617307648;804875731661692928"
2025-06-12T18:17:14+08:00,1749723434230155,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:17:13,923 [docker-java-stream--1473614770] [DEBUG] [EventsCallbackHandler] [trace_id]= span_id= trace_flags= [PlaygroundId]= [DockerId]= - DockerScheduling-docker-event, docker监听:{""status"":""start"",""id"":""9ac1922be46b0c0e3b5f34f249e775cc74d628005948f005ca0b77b74028653c"",""from"":""ghcr.io/dao42/app/docker:develop-20250516"",""type"":""CONTAINER"",""action"":""start"",""actor"":{""id"":""9ac1922be46b0c0e3b5f34f249e775cc74d628005948f005ca0b77b74028653c"",""attributes"":{""image"":""ghcr.io/dao42/app/docker:develop-20250516"",""name"":""dev-804877517617307648"",""org.opencontainers.image.ref.name"":""ubuntu"",""org.opencontainers.image.version"":""22.04""},""rawValues"":{}},""time"":1749723433,""timeNano"":1749723433916676568,""rawValues"":{""Type"":""container"",""Action"":""start"",""Actor"":{""ID"":""9ac1922be46b0c0e3b5f34f249e775cc74d628005948f005ca0b77b74028653c"",""Attributes"":{""image"":""ghcr.io/dao42/app/docker:develop-20250516"",""name"":""dev-804877517617307648"",""org.opencontainers.image.ref.name"":""ubuntu"",""org.opencontainers.image.version"":""22.04""}},""scope"":""local"",""from"":""ghcr.io/dao42/app/docker:develop-20250516"",""timeNano"":1749723433916676568,""id"":""9ac1922be46b0c0e3b5f34f249e775cc74d628005948f005ca0b77b74028653c"",""time"":1749723433,""status"":""start""}}"
2025-06-12T18:17:14+08:00,1749723434230616,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:17:13,933 [paas-async-60] [INFO ] [DockerService] [trace_id]=1a59c6b59d87b6f9f6d362e265a54151 span_id=e56a99c2903743c2 trace_flags=01 [PlaygroundId]= [DockerId]=804877517617307648 - DockerScheduling-Active, 主容器启动成功, id: 804877517617307648"
2025-06-12T18:17:14+08:00,1749723434230617,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:17:13,942 [paas-async-60] [INFO ] [DockerService] [trace_id]=1a59c6b59d87b6f9f6d362e265a54151 span_id=e56a99c2903743c2 trace_flags=01 [PlaygroundId]= [DockerId]=804877517617307648 - DockerScheduling-Active-startContainer-end, 主容器启动, id: 804877517617307648"
2025-06-12T18:17:14+08:00,1749723434230618,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:17:13,942 [paas-async-60] [INFO ] [PlaygroundService] [trace_id]=1a59c6b59d87b6f9f6d362e265a54151 span_id=e56a99c2903743c2 trace_flags=01 [PlaygroundId]= [DockerId]= - DockerScheduling-Active, active end(NOT_INIT),  playgroundId: 804877517583753216, dockerId: 804877517617307648, cost_time: 1887 ms"
2025-06-12T18:17:18+08:00,**********761205,clacky-ai-agent-staging-9dcdcc6fc-mfzff,"{""timestamp"": ""2025-06-12T10:17:18.761205"", ""level"": ""INFO"", ""service"": ""ai-agent"", ""file"": ""/app/heracles/server/clacky/ide_server_client.py:375"", ""message"": ""playground_info: {'messageId': 'bd66c89e-5695-4fb2-9a27-c3456879054d', 'timestamp': **********, 'replyMessageId': '', 'status': 'INACTIVE', 'dockerId': '804877517617307648', 'fileRootPath': '/app/data/codeZone/2025/1/6-10/@4047617b-ef8b-4064-9bb3-8f26b67d92e9/dependency/home/<USER>', 'fileRootId': 'home', 'lspRootPath': '/home/<USER>/app', 'fileTreeIgnore': '.git/;.1024.nix;.1024feature*;.nfs*;*.dll;*.swp;.paas-unit-*;core.*;.breakpoints;.idea/;.vscode/;.1024feature-file;.pnpm-store/;node_modules/', 'fileTreePackage': 'go.mod', 'url': '', 'lspLanguageId': 'Go', 'language': 'Go', 'lspSupported': True, 'gui': False, 'debugSupport': False, 'debugState': 'stop', 'defaultOpenFile': '', 'refresh': True, 'realTimeRefresh': False, 'intervalTime': 0, 'environmentVersion': {'id': 403710021818515460, 'envCode': 'code_go_1_23', 'name': 'Go 1.23', 'runtime': 'go1.23 and node v20.1.0 already installed, based on Ubuntu 22.04.4', 'packageManagers': ['go 1.23', 'npm 9.6.4'], 'language': 'Go', 'runtimeInformation': ['bash 5.1', 'git 2.34.1', 'mysql-client-8.0 8.0.40', 'mongodb-mongosh 2.3.8', 'postgresql-client-14 14.15', 'redis-tools 6.0.16', 'sqlite3 3.37.2'], 'tags': ['Pure Language']}, 'vncSupport': False, 'lspStatus': 'uninitialized', 'openedTerminalList': [], 'leaveTime': None, 'agentUsers': [{'agentUserId': '4313dfe8-4c7c-4be4-bd5c-7b33b82743c9', 'userId': '796490916608253953', 'userInfo': {'userId': 'user_2lxwpMj4OvyUlM6Eanz5eC200v9', 'username': 'xiaoyougithub', 'avatarUrl': 'https://img.clerk.com/eyJ0eXBlIjoicHJveHkiLCJzcmMiOiJodHRwczovL2ltYWdlcy5jbGVyay5kZXYvb2F1dGhfZ2l0aHViL2ltZ18ybHh3cElaNmtZMmVhb3Rqd1FSZjFPcjNUcUoifQ'}, 'fileOpened': None, 'status': 'offline', 'followingAgentUserId': '', 'focusComponent': None, 'focusXterm': None, 'editorScroll': 0, 'cursor': {}, 'wsClientID': 'rOKaNeUoEvd64gkoAAGX', 'color': '#3091F2'}, {'agentUserId': 'clacky', 'userId': '728393879279337472', 'userInfo': {'username': 'Clacky', 'avatarUrl': 'https://assetscdn.clacky.ai/clacky/marvin_avatar_common.svg', 'userId': 'clacky'}, 'status': 'o... (truncated, original length: 2420)"", ""playground_id"": ""804877517583753216"", ""life_id"": ""137c567d-4c19-479c-be11-34d5a03979cd"", ""event_name"": ""syncPlaygroundInfo""}"
2025-06-12T18:17:19+08:00,1749723439005563,paas-ide-server-785b85dffd-zrn4h,"[Nest] 44  - 06/12/2025, 6:17:18 PM    INFO [PlaygroundChannel] [mqName:paas-ide-server-785b85dffd-zrn4h][agentUserId:clacky][playgroundId:804877517583753216][playgroundChannel.ts:835] << self[clacky] syncPlaygroundInfo, {""messageId"":""bd66c89e-5695-4fb2-9a27-c3456879054d"",""timestamp"":**********,""replyMessageId"":"""",""status"":""INACTIVE"",""dockerId"":""804877517617307648"",""fileRootPath"":""/app/data/codeZone/2025/1/6-10/@4047617b-ef8b-4064-9bb3-8f26b67d92e9/dependency/home/<USER>"",""fileRootId"":""home"",""lspRootPath"":""/home/<USER>/app"",""fileTreeIgnore"":"".git/;.1024.nix;.1024feature*;.nfs*;*.dll;*.swp;.paas-unit-*;core.*;.breakpoints;.idea/;.vscode/;.1024feature-file;.pnpm-store/;node_modules/"",""fileTreePackage"":""go.mod"",""url"":"""",""lspLanguageId"":""Go"",""language"":""Go"",""lspSupported"":true,""gui"":false,""debugSupport"":false,""debugState"":""stop"",""defaultOpenFile"":"""",""refresh"":true,""realTimeRefresh"":false,""intervalTime"":0,""environmentVersion"":{""id"":403710021818515460,""envCode"":""code_go_1_23"",""name"":""Go 1.23"",""runtime"":""go1.23 and node v20.1.0 already installed, based on Ubuntu 22.04.4"",""packageManagers"":[""go 1.23"",""npm 9.6.4""],""language"":""Go"",""runtimeInformation"":[""bash 5.1"",""git 2.34.1"",""mysql-client-8.0 8.0.40"",""mongodb-mongosh 2.3.8"",""postgresql-client-14 14.15"",""redis-tools 6.0.16"",""sqlite3 3.37.2""],""tags"":[""Pure Language""]},""vncSupport"":false,""lspStatus"":""uninitialized"",""openedTerminalList"":[],""leaveTime"":null,""fileTree"":{""type"":""DIRECTORY"",""name"":""."",""path"":""."",""children"":[{""type"":""FILE"",""name"":"".1024"",""path"":"".1024"",""children"":[],""hide"":false,""lock"":false,""unittest"":false,""isRetainedFile"":false},{""type"":""FILE"",""name"":"".1024nix"",""path"":"".1024nix"",""children"":[],""hide"":false,""lock"":false,""unittest"":false,""isRetainedFile"":false},{""type"":""FILE"",""name"":"".gitignore"",""path"":"".gitignore"",""children"":[],""hide"":false,""lock"":false,""unittest"":false,""isRetainedFile"":false},{""type"":""FILE"",""name"":""README.md"",""path"":""README.md"",""children"":[],""hide"":false,""lock"":false,""unittest"":false,""isRetainedFile"":false},{""type"":""FILE"",""name"":""main.go"",""path"":""main.go"",""children"":[],""hide"":false,""lock"":false,""unittest"":false,""isRetainedFile"":false}]},""agentUsers"":[{""agentUserId"":""4313dfe8-4c7c-4b... +2ms"
2025-06-12T18:17:19+08:00,1749723439239217,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:17:18,779 [paas-async-35] [INFO ] [PlaygroundService] [trace_id]=19f4780fbe3fbe5659bef573a6839560 span_id=53af6c5cf5fa5f58 trace_flags=01 [PlaygroundId]=804877517583753216 [DockerId]=804877517617307648 - DockerScheduling-Active, active start, playgroundId: 804877517583753216,dockerId: 804877517617307648,docker status: START_SUCCESS"
2025-06-12T18:17:19+08:00,****************,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:17:18,779 [paas-async-35] [DEBUG] [MQMessageSender] [trace_id]=19f4780fbe3fbe5659bef573a6839560 span_id=53af6c5cf5fa5f58 trace_flags=01 [PlaygroundId]=804877517583753216 [DockerId]=804877517617307648 - RoutingKey: toDocker.804877517583753216.804877517617307648, Type: heartbeat, Content: {""messageId"":""c1018c41-11c9-4c65-bae6-3b93a0e44cdb"",""timestamp"":**********}"
2025-06-12T18:17:19+08:00,****************,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:17:18,780 [paas-async-35] [INFO ] [PlaygroundActiveMQHandler] [trace_id]=19f4780fbe3fbe5659bef573a6839560 span_id=53af6c5cf5fa5f58 trace_flags=01 [PlaygroundId]=804877517583753216 [DockerId]=804877517617307648 - DockerScheduling-Active-end, Activating Playground：804877517583753216, cost_time: 13 ms"
2025-06-12T18:17:19+08:00,****************,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:17:18,780 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-48] [DEBUG] [AlterMQReceiver] [trace_id]=19f4780fbe3fbe5659bef573a6839560 span_id=14aeb6caba62817d trace_flags=01 [PlaygroundId]=804877436851789824 [DockerId]=804877437032144896 - AlterMQMsg:(Body:'[B@5e81ff8f(byte[75])' MessageProperties [headers={traceparent=00-19f4780fbe3fbe5659bef573a6839560-1289c0bb9cc25bfb-01}, type=heartbeat, contentType=application/octet-stream, contentLength=0, receivedDeliveryMode=PERSISTENT, priority=10, redelivered=false, receivedExchange=paas, receivedRoutingKey=toDocker.804877517583753216.804877517617307648, deliveryTag=82, consumerTag=amq.ctag-Q7CgSumROS_xH1XeLlZloA, consumerQueue=exception])"
2025-06-12T18:17:19+08:00,****************,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:17:18,786 [paas-async-35] [INFO ] [PlaygroundActiveMQHandler] [trace_id]=19f4780fbe3fbe5659bef573a6839560 span_id=53af6c5cf5fa5f58 trace_flags=01 [PlaygroundId]=804877517583753216 [DockerId]=804877517617307648 - DockerScheduling-Active-end, PlaygroundActiveMQHandler, routingKey = toPlayground.804877517583753216, playgroundId=804877517583753216, cost_time: 19 ms"
2025-06-12T18:17:28+08:00,****************,paas-manager-staging-7c449c6d95-tldsw,"2025-06-12 18:17:27,894 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-18] [DEBUG] [IDEServerMQReceiver] [trace_id]=6ecd331788fe34565cc7b9e84419af2a span_id=82e474f0287ec172 trace_flags=01 [PlaygroundId]= [DockerId]= - managerListener receive message[mqStart](toManager) -> {""messageId"":""712c49eb-4776-11f0-bdb2-0242ac110007"",""timestamp"":**********,""replyMessageId"":"""",""dockerId"":""804877517617307648"",""ideServerCode"":""""}"
2025-06-12T18:17:35+08:00,****************,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:17:34,605 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-64] [DEBUG] [AlterMQReceiver] [trace_id]=761e09fcd56388c30466286b5cb0d6c5 span_id=e96313becf46aa1a trace_flags=01 [PlaygroundId]=804877517583753216 [DockerId]=804877517617307648 - AlterMQMsg:(Body:'{""messageId"":""752c5776-4776-11f0-91a9-0242ac11000a"",""timestamp"":**********,""replyMessageId"":"""",""memoryCurrent"":581,""memoryMax"":4096,""cpuPercent"":0.*****************}' MessageProperties [headers={}, type=resourceMonitoring, contentType=text/plain, contentLength=0, redelivered=false, receivedExchange=paas, receivedRoutingKey=paas-ide-server-5bbb9d5687-drtkz.fromDocker.804538757603889152.804538757683580928, deliveryTag=82, consumerTag=amq.ctag-TywkCVpDtbSMtHc_Vwa0pg, consumerQueue=exception])"
2025-06-12T18:17:35+08:00,****************,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:17:34,606 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-72] [DEBUG] [AlterMQReceiver] [trace_id]=09232e023d5f2d8fe5970a3a947bad3f span_id=45f58ce7fdc81f59 trace_flags=01 [PlaygroundId]=804877517583753216 [DockerId]=804877517617307648 - AlterMQMsg:(Body:'{""messageId"":""752c99e3-4776-11f0-834f-0242ac11000c"",""timestamp"":**********,""replyMessageId"":"""",""memoryCurrent"":583,""memoryMax"":4096,""cpuPercent"":0.***************}' MessageProperties [headers={}, type=resourceMonitoring, contentType=text/plain, contentLength=0, redelivered=false, receivedExchange=paas, receivedRoutingKey=paas-ide-server-9bb46d5db-shmk5.fromDocker.804820000325459968.804820000350625792, deliveryTag=82, consumerTag=amq.ctag-yu_D3UgqCrOKRIQLITuUqw, consumerQueue=exception])"
2025-06-12T18:17:51+08:00,****************,paas-manager-staging-7c449c6d95-tldsw,"2025-06-12 18:17:51,570 [redisMessageListenerContainer-797] [DEBUG] [RedisKeyExpirationListener] [trace_id]=806644635ff111bbe4cf4bf1bc9ddaf3 span_id=52349e5c40c339b3 trace_flags=01 [PlaygroundId]= [DockerId]= - RedisMessage:mq:waitDockerInfo:804877517617307648"
2025-06-12T18:17:51+08:00,****************,paas-manager-staging-7c449c6d95-tldsw,"2025-06-12 18:17:51,571 [redisMessageListenerContainer-797] [DEBUG] [DockerService] [trace_id]=806644635ff111bbe4cf4bf1bc9ddaf3 span_id=52349e5c40c339b3 trace_flags=01 [PlaygroundId]= [DockerId]= - sendActiveFailResult-激活失败：804877517617307648"
2025-06-12T18:17:52+08:00,****************,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:17:51,571 [redisMessageListenerContainer-797] [DEBUG] [RedisKeyExpirationListener] [trace_id]=4817b6388d1e1ec80773b7a4218128f1 span_id=fb8b6441d34a7f3c trace_flags=01 [PlaygroundId]= [DockerId]= - Redisson get lock fail:RedisMessage:804877517617307648"
2025-06-12T18:17:52+08:00,1749723472973239,paas-manager-staging-7c449c6d95-tldsw,"2025-06-12 18:17:52,350 [redisMessageListenerContainer-797] [DEBUG] [RedisKeyExpirationListener] [trace_id]=806644635ff111bbe4cf4bf1bc9ddaf3 span_id=52349e5c40c339b3 trace_flags=01 [PlaygroundId]= [DockerId]= - Redisson get lock :RedisMessage:redisson:redis:expiration:804877517617307648"
2025-06-12T18:17:54+08:00,1749723474795545,,"time=2025-06-12T06:17:27.892-04:00 level=INFO msg=""Send/toManager/mqStart:{\""messageId\"":\""712c49eb-4776-11f0-bdb2-0242ac110007\"",\""timestamp\"":**********,\""replyMessageId\"":\""\"",\""dockerId\"":\""804877517617307648\"",\""ideServerCode\"":\""\""}"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:17:54+08:00,1749723474795546,,"time=2025-06-12T06:17:27.900-04:00 level=INFO msg=""RPC connection established with 30.0.129.38:5672"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:17:54+08:00,1749723474795547,,"time=2025-06-12T06:17:27.902-04:00 level=INFO msg=""RPC channel creation success"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:17:54+08:00,1749723474795769,,"time=2025-06-12T06:17:27.916-04:00 level=INFO msg=""processRpcMessages: consume message "" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482094006,clacky-ai-agent-staging-9dcdcc6fc-mfzff,"{""timestamp"": ""2025-06-12T10:18:02.094006"", ""level"": ""DEBUG"", ""service"": ""ai-agent"", ""file"": ""/app/heracles/server/clacky/ide_server_client.py:553"", ""message"": ""({'eventName': 'console', 'timestamp': 1749723482092, 'playgroundId': '804877517583753216', 'agentUserId': 'backend', 'dockerId': '804877517617307648', 'data': {'value': 'PaasNewLineSign'}},)"", ""playground_id"": ""804877517583753216"", ""life_id"": ""137c567d-4c19-479c-be11-34d5a03979cd"", ""event_name"": ""console""}"
2025-06-12T18:18:02+08:00,1749723482110935,paas-ide-server-785b85dffd-zrn4h,"[Nest] 44  - 06/12/2025, 6:18:02 PM    INFO [RabbitmqService] [mqName:paas-ide-server-785b85dffd-zrn4h][playgroundId:804877517583753216][rabbitmq.service.ts:129] <=[fromMQ] runStatus[804877517583753216]:{""messageId"":""858e5989-4776-11f0-bdb2-0242ac110007"",""timestamp"":1749723482,""replyMessageId"":"""",""terminalId"":"""",""terminalType"":"""",""status"":"""",""runResult"":"""",""dockerId"":""804877517617307648"",""playgroundId"":""804877517583753216"",""gui"":false,""autoImport"":{""output"":"""",""err"":null,""duration"":0},""compile"":{""output"":"""",""err"":null,""duration"":0},""run"":{""output"":"""",""err"":null,""duration"":0}} +1ms"
2025-06-12T18:18:02+08:00,1749723482360873,clacky-ai-agent-staging-9dcdcc6fc-mfzff,"{""timestamp"": ""2025-06-12T10:18:02.360873"", ""level"": ""DEBUG"", ""service"": ""ai-agent"", ""file"": ""/app/heracles/server/clacky/ide_server_client.py:553"", ""message"": ""({'timestamp': 1749723482342, 'playgroundId': '804877517583753216', 'dockerId': '804877517617307648', 'eventName': 'terminal', 'agentUserId': 'backend', 'data': {'value': 'PaasNewLineSign'}, '_id': '684aa95aa1891390c03292c5', '__v': 0},)"", ""playground_id"": ""804877517583753216"", ""life_id"": ""137c567d-4c19-479c-be11-34d5a03979cd"", ""event_name"": ""terminal""}"
2025-06-12T18:18:02+08:00,1749723482798036,,"time=2025-06-12T06:18:02.089-04:00 level=INFO msg=""ReceiveMessage: mountSuccess , {\""messageId\"":\""712c49eb-4776-11f0-bdb2-0242ac110007\"",\""timestamp\"":**********,\""replyMessageId\"":\""\"",\""dockerId\"":\""804877517617307648\"",\""ideServerCode\"":\""paas-ide-server-785b85dffd-zrn4h\""}"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798037,,"time=2025-06-12T06:18:02.089-04:00 level=INFO msg=""MultiTerminal: Initializing multi-terminal system..."" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798038,,"time=2025-06-12T06:18:02.089-04:00 level=INFO msg=""Multiterminal: system initialized successfully"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798039,,"time=2025-06-12T06:18:02.089-04:00 level=INFO msg=""Send/paas-ide-server-785b85dffd-zrn4h.fromDocker.804877517583753216.804877517617307648/console:{\""messageId\"":\""858e5346-4776-11f0-bdb2-0242ac110007\"",\""timestamp\"":1749723482,\""replyMessageId\"":\""\"",\""value\"":\""PaasNewLineSign\"",\""terminalId\"":\""\"",\""terminalType\"":\""\""}"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798040,,"time=2025-06-12T06:18:02.089-04:00 level=INFO msg=""Send/paas-ide-server-785b85dffd-zrn4h.fromDocker.804877517583753216.804877517617307648/terminalStatus:{\""messageId\"":\""858e5334-4776-11f0-bdb2-0242ac110007\"",\""timestamp\"":1749723482,\""replyMessageId\"":\""\"",\""value\"":\""loading\"",\""terminalId\"":\""\"",\""terminalType\"":\""\""}"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798041,,"time=2025-06-12T06:18:02.089-04:00 level=INFO msg=""Send/paas-ide-server-785b85dffd-zrn4h.fromDocker.804877517583753216.804877517617307648/runStatus:{\""messageId\"":\""858e5989-4776-11f0-bdb2-0242ac110007\"",\""timestamp\"":1749723482,\""replyMessageId\"":\""\"",\""terminalId\"":\""\"",\""terminalType\"":\""\"",\""status\"":\""\"",\""runResult\"":\""\"",\""dockerId\"":\""804877517617307648\"",\""playgroundId\"":\""804877517583753216\"",\""gui\"":false,\""autoImport\"":{\""output\"":\""\"",\""err\"":null,\""duration\"":0},\""compile\"":{\""output\"":\""\"",\""err\"":null,\""duration\"":0},\""run\"":{\""output\"":\""\"",\""err\"":null,\""duration\"":0}}"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798042,,"time=2025-06-12T06:18:02.089-04:00 level=INFO msg=""Send/paas-ide-server-785b85dffd-zrn4h.fromDocker.804877517583753216.804877517617307648/resourceMonitoring:{\""messageId\"":\""858e5c18-4776-11f0-bdb2-0242ac110007\"",\""timestamp\"":1749723482,\""replyMessageId\"":\""\"",\""memoryCurrent\"":38,\""memoryMax\"":4096,\""cpuPercent\"":0.010417763207362677}"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798043,,"time=2025-06-12T06:18:02.089-04:00 level=INFO msg=""RagStatusToMqChannel:{{858e5f85-4776-11f0-bdb2-0242ac110007 1749723482 } RagInit}"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798044,,"time=2025-06-12T06:18:02.090-04:00 level=INFO msg=""Send/paas-ide-server-785b85dffd-zrn4h.fromDocker.804877517583753216.804877517617307648/ragStatus:{\""messageId\"":\""858e5f85-4776-11f0-bdb2-0242ac110007\"",\""timestamp\"":1749723482,\""replyMessageId\"":\""\"",\""value\"":\""RagInit\""}"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798045,,"time=2025-06-12T06:18:02.090-04:00 level=INFO msg=""httpProxy:HttpMainProxy, Starting proxy server on 0.0.0.0:10086"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798046,,"time=2025-06-12T06:18:02.090-04:00 level=WARN msg=""MultiLspServer, lsp server Start languages: Go,Typescript, defaultLanguage: Go"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798047,,"time=2025-06-12T06:18:02.090-04:00 level=INFO msg=""SendMessage client Do failed:Post \""xxx\"": unsupported protocol scheme \""\"""" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798048,,"time=2025-06-12T06:18:02.090-04:00 level=INFO msg=""MultiLspServer, lsp start, language: Go, lspCmd: gopls -mode=stdio"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798049,,"time=2025-06-12T06:18:02.090-04:00 level=INFO msg=""Send/paas-ide-server-785b85dffd-zrn4h.fromDocker.804877517583753216.804877517617307648/lspStatus:{\""messageId\"":\""858e7afd-4776-11f0-bdb2-0242ac110007\"",\""timestamp\"":1749723482,\""replyMessageId\"":\""\"",\""status\"":\""loading\"",\""language\"":\""Go\""}"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798050,,"time=2025-06-12T06:18:02.218-04:00 level=INFO msg=""Terminal >> terminal start cmd.bash"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798051,,"time=2025-06-12T06:18:02.339-04:00 level=INFO msg=""Terminal >> terminal start OK"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798052,,"time=2025-06-12T06:18:02.339-04:00 level=INFO msg=""Terminal >> isNeedWaitInit: false"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798053,,"time=2025-06-12T06:18:02.339-04:00 level=INFO msg=""Send/paas-ide-server-785b85dffd-zrn4h.fromDocker.804877517583753216.804877517617307648/terminal:{\""messageId\"":\""85b47761-4776-11f0-bdb2-0242ac110007\"",\""timestamp\"":1749723482,\""replyMessageId\"":\""\"",\""value\"":\""PaasNewLineSign\"",\""terminalId\"":\""\"",\""terminalType\"":\""\""}"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798054,,"time=2025-06-12T06:18:02.339-04:00 level=INFO msg=""Send/paas-ide-server-785b85dffd-zrn4h.fromDocker.804877517583753216.804877517617307648/terminalStatus:{\""messageId\"":\""85b47ae1-4776-11f0-bdb2-0242ac110007\"",\""timestamp\"":1749723482,\""replyMessageId\"":\""\"",\""value\"":\""running\"",\""terminalId\"":\""\"",\""terminalType\"":\""\""}"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798055,,"time=2025-06-12T06:18:02.397-04:00 level=INFO msg=""Send/toManager/updateDockerInfo:{\""messageId\"":\""************************************\"",\""timestamp\"":1749723482,\""replyMessageId\"":\""\"",\""dockerId\"":\""804877517617307648\"",\""playgroundId\"":\""804877517583753216\"",\""runStatus\"":\""STOP\"",\""language\"":\""\"",\""languageVersion\"":\""\"",\""framework\"":\""\"",\""frameworkVersion\"":\""\"",\""needRunButton\"":false,\""needConsole\"":false,\""needBrowser\"":false,\""url\"":\""\"",\""lspStatus\"":\""loading\"",\""lspUrl\"":\""\"",\""lspRoot\"":\""\"",\""terminalStatus\"":\""loading\"",\""ragStatus\"":\""RagInit\"",\""gui\"":false,\""vncSupport\"":true,\""debugSupport\"":false,\""refresh\"":true,\""intervalTime\"":0}"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798056,,"time=2025-06-12T06:18:02.397-04:00 level=INFO msg=""MultiLspServer, lsp start : &{ctx:0xc00019d9f0 websocket:0xc0001b0910 cancelFunc:0x4baa00 isOldLsp:false protoVersion:0 diagnosticResponseChannels:map[] mutex:{_:{} mu:{state:0 sema:0}} cmd:bash arg:[-c source ~/.bashrc && gopls -mode=stdio] execCmd:0xc0002d4180 language:Go rootUri:file:///home/<USER>/app lspJsonRpcConn:0xc0008960d8 startHook:0xe1e760 initializeResponse:<nil> cmdUtilService:0x5112fa0}, language lsp: Go"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798057,,"time=2025-06-12T06:18:02.398-04:00 level=INFO msg=""Send/toManager/updateDockerInfo:{\""messageId\"":\""************************************\"",\""timestamp\"":1749723482,\""replyMessageId\"":\""\"",\""dockerId\"":\""804877517617307648\"",\""playgroundId\"":\""804877517583753216\"",\""runStatus\"":\""STOP\"",\""language\"":\""\"",\""languageVersion\"":\""\"",\""framework\"":\""\"",\""frameworkVersion\"":\""\"",\""needRunButton\"":false,\""needConsole\"":false,\""needBrowser\"":false,\""url\"":\""\"",\""lspStatus\"":\""loading\"",\""lspUrl\"":\""\"",\""lspRoot\"":\""\"",\""terminalStatus\"":\""loading\"",\""ragStatus\"":\""RagInit\"",\""gui\"":false,\""vncSupport\"":true,\""debugSupport\"":false,\""refresh\"":true,\""intervalTime\"":0}"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798058,,"time=2025-06-12T06:18:02.399-04:00 level=INFO msg=""Send/toManager/dockerInfo:{\""messageId\"":\""************************************\"",\""timestamp\"":1749723482,\""replyMessageId\"":\""\"",\""dockerId\"":\""804877517617307648\"",\""playgroundId\"":\""804877517583753216\"",\""runStatus\"":\""STOP\"",\""language\"":\""\"",\""languageVersion\"":\""\"",\""framework\"":\""\"",\""frameworkVersion\"":\""\"",\""needRunButton\"":false,\""needConsole\"":false,\""needBrowser\"":false,\""url\"":\""\"",\""lspStatus\"":\""loading\"",\""lspUrl\"":\""\"",\""lspRoot\"":\""\"",\""terminalStatus\"":\""loading\"",\""ragStatus\"":\""RagInit\"",\""gui\"":false,\""vncSupport\"":true,\""debugSupport\"":false,\""refresh\"":true,\""intervalTime\"":0}"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798059,,"time=2025-06-12T06:18:02.399-04:00 level=INFO msg=""Listening signals..."" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798062,,"time=2025-06-12T06:18:02.399-04:00 level=INFO msg=""[ProcessManager] 执行 DBUS-SYSTEM 的运行前检查...\n"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798063,,"time=2025-06-12T06:18:02.399-04:00 level=INFO msg=""[ProcessManager] 检查并设置D-Bus系统总线环境..."" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798064,,"time=2025-06-12T06:18:02.399-04:00 level=INFO msg=""LangLinters, Starting periodic language check goroutine."" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798065,,"time=2025-06-12T06:18:02.400-04:00 level=INFO msg=""LangLinters, Starting periodic language check goroutine."" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798066,,"time=2025-06-12T06:18:02.400-04:00 level=INFO msg=""LangLinters, Registered handler for event type: file_open"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798067,,"time=2025-06-12T06:18:02.400-04:00 level=INFO msg=""LangLinters, Registered handler for event type: container_start"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798068,,"time=2025-06-12T06:18:02.400-04:00 level=INFO msg=""LangLinters, Registered handler for event type: file_change"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798069,,"time=2025-06-12T06:18:02.400-04:00 level=INFO msg=""LangLinters, Detecting project structure and installing appropriate linters..."" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798070,,"time=2025-06-12T06:18:02.400-04:00 level=INFO msg=""LangLinters, Checking for specific linter configuration files..."" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798071,,"time=2025-06-12T06:18:02.400-04:00 level=INFO msg=""[ProcessManager] D-Bus系统总线环境检查完成"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798072,,"time=2025-06-12T06:18:02.400-04:00 level=INFO msg=""[ProcessManager] DBUS-SYSTEM 运行前检查通过\n"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798073,,"time=2025-06-12T06:18:02.400-04:00 level=INFO msg=""[ProcessManager] 启动进程 DBUS-SYSTEM...\n"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798074,,"time=2025-06-12T06:18:02.400-04:00 level=INFO msg=""mysqlProxyMain, TCP Proxy server is running on :3306"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798075,,"time=2025-06-12T06:18:02.400-04:00 level=INFO msg=""postgresqlProxyMain, TCP Proxy server is running on :5432"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798076,,"time=2025-06-12T06:18:02.400-04:00 level=INFO msg=""mongoProxyMain, TCP Proxy server is running on :27017"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798077,,"time=2025-06-12T06:18:02.400-04:00 level=INFO msg=""redisProxyMain, TCP Proxy server is running on :6379"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,1749723482798084,,"2025/06/12 10:18:02.446	[34mINFO[0m	Send/paas-ide-server-785b85dffd-zrn4h.fromDocker.804877517583753216.804877517617307648/config:{""messageId"":""85c4c015-4776-11f0-bdb2-0242ac110007"",""timestamp"":1749723482,""replyMessageId"":"""",""configs"":{""components"":[""filetree"",""shell"",""console""],""debugSupport"":false}}"
2025-06-12T18:18:02+08:00,****************,,"time=2025-06-12T06:18:02.720-04:00 level=INFO msg=""[ProcessManager] DBUS-SYSTEM 已启动，PID: 80\n"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:02+08:00,****************,paas-manager-staging-7c449c6d95-tldsw,"2025-06-12 18:18:02,086 [paas-async-117] [DEBUG] [MQMessageSender] [trace_id]=6ecd331788fe34565cc7b9e84419af2a span_id=82e474f0287ec172 trace_flags=01 [PlaygroundId]=804877517583753216 [DockerId]=804877517617307648 - RoutingKey: toDocker.804877517583753216.804877517617307648, Type: mountSuccess, Content: {""messageId"":""712c49eb-4776-11f0-bdb2-0242ac110007"",""timestamp"":**********,""replyMessageId"":"""",""dockerId"":""804877517617307648"",""ideServerCode"":""paas-ide-server-785b85dffd-zrn4h""}"
2025-06-12T18:18:02+08:00,****************,paas-manager-staging-7c449c6d95-tldsw,"2025-06-12 18:18:02,086 [paas-async-117] [INFO ] [DockerMqStartMQHandler] [trace_id]=6ecd331788fe34565cc7b9e84419af2a span_id=82e474f0287ec172 trace_flags=01 [PlaygroundId]=804877517583753216 [DockerId]=804877517617307648 - DockerScheduling-Active-MQ_START-end, Activating Playground：804877517583753216, cost_time: 34192 ms"
2025-06-12T18:18:03+08:00,****************,paas-ide-server-785b85dffd-zrn4h,"[Nest] 44  - 06/12/2025, 6:18:02 PM    INFO [PlaygroundItem] [mqName:paas-ide-server-785b85dffd-zrn4h][agentUserId:clacky][playgroundId:804877517583753216][playgroundItem.ts:553] << all:terminal:{""$__"":{""activePaths"":{""paths"":{},""states"":{""ignore"":{},""default"":{},""init"":{},""modify"":{},""require"":{}},""stateNames"":[""require"",""modify"",""init"",""default"",""ignore""]},""strictMode"":true,""op"":null,""saving"":null,""$versionError"":null,""saveOptions"":null,""validating"":null,""cachedRequired"":{},""backup"":{""activePaths"":{""modify"":{""timestamp"":true,""playgroundId"":true,""dockerId"":true,""eventName"":true,""agentUserId"":true,""data"":true},""default"":{""_id"":true}},""validationError"":null},""inserting"":true,""savedState"":{}},""$isNew"":false,""_doc"":{""timestamp"":1749723482342,""playgroundId"":""804877517583753216"",""dockerId"":""804877517617307648"",""eventName"":""terminal"",""agentUserId"":""backend"",""data"":{""value"":""PaasNewLineSign""},""_id"":{},""__v"":0}} +16ms"
2025-06-12T18:18:03+08:00,1749723483349464,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:18:02,398 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-12] [DEBUG] [IDEServerMQReceiver] [trace_id]=a4b0477e303b145f126bac7ee9181ecd span_id=208ff88b1a977603 trace_flags=01 [PlaygroundId]= [DockerId]= - managerListener receive message[updateDockerInfo](toManager) -> {""messageId"":""************************************"",""timestamp"":1749723482,""replyMessageId"":"""",""dockerId"":""804877517617307648"",""playgroundId"":""804877517583753216"",""runStatus"":""STOP"",""language"":"""",""languageVersion"":"""",""framework"":"""",""frameworkVersion"":"""",""needRunButton"":false,""needConsole"":false,""needBrowser"":false,""url"":"""",""lspStatus"":""loading"",""lspUrl"":"""",""lspRoot"":"""",""terminalStatus"":""loading"",""ragStatus"":""RagInit"",""gui"":false,""vncSupport"":true,""debugSupport"":false,""refresh"":true,""intervalTime"":0}"
2025-06-12T18:18:03+08:00,1749723483349466,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:18:02,399 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-11] [DEBUG] [IDEServerMQReceiver] [trace_id]=7043401e4d26e25cb077586ddd1a9169 span_id=706d38818fc8596e trace_flags=01 [PlaygroundId]= [DockerId]= - managerListener receive message[updateDockerInfo](toManager) -> {""messageId"":""************************************"",""timestamp"":1749723482,""replyMessageId"":"""",""dockerId"":""804877517617307648"",""playgroundId"":""804877517583753216"",""runStatus"":""STOP"",""language"":"""",""languageVersion"":"""",""framework"":"""",""frameworkVersion"":"""",""needRunButton"":false,""needConsole"":false,""needBrowser"":false,""url"":"""",""lspStatus"":""loading"",""lspUrl"":"""",""lspRoot"":"""",""terminalStatus"":""loading"",""ragStatus"":""RagInit"",""gui"":false,""vncSupport"":true,""debugSupport"":false,""refresh"":true,""intervalTime"":0}"
2025-06-12T18:18:03+08:00,1749723483349468,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:18:02,400 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-14] [DEBUG] [IDEServerMQReceiver] [trace_id]=87f4d1230f53c5720375776426e87317 span_id=99ba315161b3548a trace_flags=01 [PlaygroundId]= [DockerId]= - managerListener receive message[dockerInfo](toManager) -> {""messageId"":""************************************"",""timestamp"":1749723482,""replyMessageId"":"""",""dockerId"":""804877517617307648"",""playgroundId"":""804877517583753216"",""runStatus"":""STOP"",""language"":"""",""languageVersion"":"""",""framework"":"""",""frameworkVersion"":"""",""needRunButton"":false,""needConsole"":false,""needBrowser"":false,""url"":"""",""lspStatus"":""loading"",""lspUrl"":"""",""lspRoot"":"""",""terminalStatus"":""loading"",""ragStatus"":""RagInit"",""gui"":false,""vncSupport"":true,""debugSupport"":false,""refresh"":true,""intervalTime"":0}"
2025-06-12T18:18:03+08:00,1749723483349857,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:18:02,401 [paas-async-137] [DEBUG] [RedisExternalService] [trace_id]=87f4d1230f53c5720375776426e87317 span_id=99ba315161b3548a trace_flags=01 [PlaygroundId]= [DockerId]= - redis >>> get mq:waitDockerInfo:804877517617307648 -> null"
2025-06-12T18:18:03+08:00,1749723483349858,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:18:02,401 [paas-async-137] [WARN ] [DockerInfoMQHandler] [trace_id]=87f4d1230f53c5720375776426e87317 span_id=99ba315161b3548a trace_flags=01 [PlaygroundId]= [DockerId]=804877517617307648 - DockerScheduling-Active, getMQWaitDockerInfo timeout(30s), dockerId: 804877517617307648"
2025-06-12T18:18:03+08:00,1749723483799587,,"time=2025-06-12T06:18:02.920-04:00 level=INFO msg=""[ProcessManager] 执行 DBUS 的运行前检查...\n"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:03+08:00,1749723483799588,,"time=2025-06-12T06:18:02.920-04:00 level=INFO msg=""[ProcessManager] 检查并设置D-Bus环境..."" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:03+08:00,1749723483799589,,"time=2025-06-12T06:18:02.920-04:00 level=INFO msg=""[ProcessManager] D-Bus环境检查完成"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:03+08:00,1749723483799590,,"time=2025-06-12T06:18:02.920-04:00 level=INFO msg=""[ProcessManager] DBUS 运行前检查通过\n"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:03+08:00,1749723483799591,,"time=2025-06-12T06:18:02.920-04:00 level=INFO msg=""[ProcessManager] 启动进程 DBUS...\n"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:03+08:00,1749723483799592,,"time=2025-06-12T06:18:02.988-04:00 level=INFO msg=""[ProcessManager] DBUS 已启动，PID: 82\n"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:03+08:00,1749723483799594,,"time=2025-06-12T06:18:03.189-04:00 level=INFO msg=""[ProcessManager] 执行 TigerVNC 的运行前检查...\n"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:03+08:00,1749723483799595,,"time=2025-06-12T06:18:03.189-04:00 level=INFO msg=""[ProcessManager] 检查并设置TigerVNC环境..."" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:03+08:00,1749723483799597,,"time=2025-06-12T06:18:03.222-04:00 level=INFO msg=""RAG:rag:SaveToRag:Start - 2025-06-12 06:18:03.222642203 -0400 EDT m=+42.907937274"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:03+08:00,1749723483799598,,"time=2025-06-12T06:18:03.222-04:00 level=INFO msg=""RagStatusToMqChannel:{{863b37e6-4776-11f0-bdb2-0242ac110007 1749723483 } RagReIndex}"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:03+08:00,1749723483799599,,"2025/06/12 10:18:03.222	[34mINFO[0m	Send/paas-ide-server-785b85dffd-zrn4h.fromDocker.804877517583753216.804877517617307648/ragStatus:{""messageId"":""863b37e6-4776-11f0-bdb2-0242ac110007"",""timestamp"":1749723483,""replyMessageId"":"""",""value"":""RagReIndex""}"
2025-06-12T18:18:03+08:00,1749723483799600,,"time=2025-06-12T06:18:03.404-04:00 level=INFO msg=""LangLinters, No specific linter configuration files detected."" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:03+08:00,1749723483799601,,"time=2025-06-12T06:18:03.404-04:00 level=INFO msg=""LangLinters, Checking project languages for default linter installation in directory: /home/<USER>/app"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:03+08:00,1749723483799981,,"time=2025-06-12T06:18:03.404-04:00 level=INFO msg=""LangLinters, Scanning directory /home/<USER>/app for languages to install default linters..."" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:04+08:00,**********800326,,"time=2025-06-12T06:18:04.225-04:00 level=INFO msg=""[ProcessManager] 清理旧VNC进程: exit status 1"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:04+08:00,**********800327,,"time=2025-06-12T06:18:04.725-04:00 level=INFO msg=""[ProcessManager] TigerVNC环境检查完成"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:04+08:00,**********800328,,"time=2025-06-12T06:18:04.725-04:00 level=INFO msg=""[ProcessManager] TigerVNC 运行前检查通过\n"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:04+08:00,**********800329,,"time=2025-06-12T06:18:04.725-04:00 level=INFO msg=""[ProcessManager] 启动进程 TigerVNC...\n"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:05+08:00,1749723485352838,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:18:04,624 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-48] [DEBUG] [AlterMQReceiver] [trace_id]=5dd58ea9940400c765a59b6c697970f3 span_id=3a550fdcd1aa7b09 trace_flags=01 [PlaygroundId]=804877517583753216 [DockerId]=804877517617307648 - AlterMQMsg:(Body:'{""messageId"":""8710fe27-4776-11f0-96a4-0242ac110015"",""timestamp"":**********,""replyMessageId"":"""",""memoryCurrent"":534,""memoryMax"":4096,""cpuPercent"":0.*****************}' MessageProperties [headers={}, type=resourceMonitoring, contentType=text/plain, contentLength=0, redelivered=false, receivedExchange=paas, receivedRoutingKey=paas-ide-server-9bb46d5db-jzlcl.fromDocker.804837439817551872.804837439851106304, deliveryTag=83, consumerTag=amq.ctag-Q7CgSumROS_xH1XeLlZloA, consumerQueue=exception])"
2025-06-12T18:18:05+08:00,****************,,"time=2025-06-12T06:18:04.844-04:00 level=INFO msg=""[ProcessManager] TigerVNC 已启动，PID: 85\n"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:05+08:00,****************,,"time=2025-06-12T06:18:05.045-04:00 level=INFO msg=""[ProcessManager] 执行 Rfbproxy 的运行前检查...\n"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:05+08:00,****************,,"time=2025-06-12T06:18:05.045-04:00 level=INFO msg=""[ProcessManager] 检查并设置Rfbproxy环境..."" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:05+08:00,****************,,"time=2025-06-12T06:18:05.045-04:00 level=INFO msg=""[ProcessManager] Rfbproxy环境检查完成"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:05+08:00,1749723485801356,,"time=2025-06-12T06:18:05.045-04:00 level=INFO msg=""[ProcessManager] Rfbproxy 运行前检查通过\n"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:05+08:00,1749723485801357,,"time=2025-06-12T06:18:05.045-04:00 level=INFO msg=""[ProcessManager] 启动进程 Rfbproxy...\n"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:05+08:00,1749723485801358,,"time=2025-06-12T06:18:05.063-04:00 level=INFO msg=""[ProcessManager] Rfbproxy 已启动，PID: 87\n"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:05+08:00,1749723485801359,,"time=2025-06-12T06:18:05.264-04:00 level=INFO msg=""[ProcessManager] 执行 Chrome 的运行前检查...\n"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:05+08:00,1749723485801360,,"time=2025-06-12T06:18:05.264-04:00 level=INFO msg=""[ProcessManager] 检查并设置Chrome环境..."" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:05+08:00,1749723485801361,,"time=2025-06-12T06:18:05.352-04:00 level=INFO msg=""[ProcessManager] 清理旧Chrome进程: exit status 1"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:05+08:00,1749723485801362,,"time=2025-06-12T06:18:05.395-04:00 level=INFO msg=""[ProcessManager] 清理Chrome调试端口: exec: \""fuser\"": executable file not found in $PATH"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:05+08:00,1749723485801363,,"time=2025-06-12T06:18:05.596-04:00 level=INFO msg=""[ProcessManager] Chrome环境检查完成"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:05+08:00,1749723485801364,,"time=2025-06-12T06:18:05.596-04:00 level=INFO msg=""[ProcessManager] Chrome 运行前检查通过\n"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:05+08:00,1749723485801365,,"time=2025-06-12T06:18:05.596-04:00 level=INFO msg=""[ProcessManager] 启动进程 Chrome...\n"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:05+08:00,1749723485801663,,"time=2025-06-12T06:18:05.617-04:00 level=INFO msg=""[ProcessManager] Chrome 已启动，PID: 89\n"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:07+08:00,1749723487803231,,"2025/06/12 10:18:07.091	[34mINFO[0m	Send/paas-ide-server-785b85dffd-zrn4h.fromDocker.804877517583753216.804877517617307648/resourceMonitoring:{""messageId"":""88898768-4776-11f0-bdb2-0242ac110007"",""timestamp"":1749723487,""replyMessageId"":"""",""memoryCurrent"":62,""memoryMax"":4096,""cpuPercent"":0.009591574460063458}"
2025-06-12T18:18:12+08:00,1749723492365544,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:18:12,365 [paas-async-78] [INFO ] [PlaygroundService] [trace_id]=758e01175b13d968c07aac04b6455fe2 span_id=585ec6c15a9acad8 trace_flags=01 [PlaygroundId]=804877517583753216 [DockerId]=804877517617307648 - DockerScheduling-Active, active start, playgroundId: 804877517583753216,dockerId: 804877517617307648,docker status: START_FAIL"
2025-06-12T18:18:12+08:00,1749723492365545,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:18:12,365 [paas-async-78] [DEBUG] [PlaygroundService] [trace_id]=758e01175b13d968c07aac04b6455fe2 span_id=585ec6c15a9acad8 trace_flags=01 [PlaygroundId]=804877517583753216 [DockerId]=804877517617307648 - DockerScheduling-Active active buildNewDocker,  playgroundId: 804877517583753216, dockerId: 804877517617307648, docker_statu: START_FAIL"
2025-06-12T18:18:12+08:00,1749723492365891,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:18:12,365 [paas-async-78] [INFO ] [PlaygroundService] [trace_id]=758e01175b13d968c07aac04b6455fe2 span_id=585ec6c15a9acad8 trace_flags=01 [PlaygroundId]=804877517583753216 [DockerId]=804877517617307648 - DockerScheduling-buildNewDocker begin,  playgroundId: 804877517583753216, BindType: CODE_ZONE"
2025-06-12T18:18:12+08:00,1749723492805483,,"2025/06/12 10:18:12.092	[34mINFO[0m	Send/paas-ide-server-785b85dffd-zrn4h.fromDocker.804877517583753216.804877517617307648/resourceMonitoring:{""messageId"":""8b84a087-4776-11f0-bdb2-0242ac110007"",""timestamp"":1749723492,""replyMessageId"":"""",""memoryCurrent"":73,""memoryMax"":4096,""cpuPercent"":0.008523706558907338}"
2025-06-12T18:18:12+08:00,1749723492805568,,"time=2025-06-12T06:18:12.401-04:00 level=INFO msg=""middlewareProxy-UpdateMiddlewareEnv timeout"" playground_id=804877517583753216 docker_id=804877517617307648"
2025-06-12T18:18:13+08:00,1749723493366364,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:18:12,365 [paas-async-78] [DEBUG] [DockerService] [trace_id]=758e01175b13d968c07aac04b6455fe2 span_id=585ec6c15a9acad8 trace_flags=01 [PlaygroundId]=804877517583753216 [DockerId]=804877517617307648 - docker-remove删除容器并回收资源:804877517617307648"
2025-06-12T18:18:13+08:00,1749723493366365,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:18:12,371 [paas-async-78] [DEBUG] [DockerExternalService] [trace_id]=758e01175b13d968c07aac04b6455fe2 span_id=585ec6c15a9acad8 trace_flags=01 [PlaygroundId]=804877517583753216 [DockerId]=804877517617307648 - 删除容器docker;containerId:804877517617307648;dockerServerId:804875731661692928"
2025-06-12T18:18:13+08:00,1749723493366743,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:18:13,314 [docker-java-stream--1473614770] [DEBUG] [EventsCallbackHandler] [trace_id]= span_id= trace_flags= [PlaygroundId]= [DockerId]= - DockerScheduling-docker-event, docker监听:{""status"":""die"",""id"":""9ac1922be46b0c0e3b5f34f249e775cc74d628005948f005ca0b77b74028653c"",""from"":""ghcr.io/dao42/app/docker:develop-20250516"",""type"":""CONTAINER"",""action"":""die"",""actor"":{""id"":""9ac1922be46b0c0e3b5f34f249e775cc74d628005948f005ca0b77b74028653c"",""attributes"":{""execDuration"":""59"",""exitCode"":""137"",""image"":""ghcr.io/dao42/app/docker:develop-20250516"",""name"":""dev-804877517617307648"",""org.opencontainers.image.ref.name"":""ubuntu"",""org.opencontainers.image.version"":""22.04""},""rawValues"":{}},""time"":1749723493,""timeNano"":1749723493306638325,""rawValues"":{""Type"":""container"",""Action"":""die"",""Actor"":{""ID"":""9ac1922be46b0c0e3b5f34f249e775cc74d628005948f005ca0b77b74028653c"",""Attributes"":{""execDuration"":""59"",""exitCode"":""137"",""image"":""ghcr.io/dao42/app/docker:develop-20250516"",""name"":""dev-804877517617307648"",""org.opencontainers.image.ref.name"":""ubuntu"",""org.opencontainers.image.version"":""22.04""}},""scope"":""local"",""from"":""ghcr.io/dao42/app/docker:develop-20250516"",""timeNano"":1749723493306638325,""id"":""9ac1922be46b0c0e3b5f34f249e775cc74d628005948f005ca0b77b74028653c"",""time"":1749723493,""status"":""die""}}"
2025-06-12T18:18:13+08:00,1749723493366744,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:18:13,348 [docker-java-stream--1473614770] [DEBUG] [MemoryDockerServerSelector] [trace_id]= span_id= trace_flags= [PlaygroundId]= [DockerId]= - 回收主容器Recycle server(804875731661692928) resource from Container(id=804877517617307648), memory:4096, cpu:2"
2025-06-12T18:18:13+08:00,1749723493366745,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:18:13,358 [paas-async-78] [DEBUG] [URLResourceService] [trace_id]=758e01175b13d968c07aac04b6455fe2 span_id=585ec6c15a9acad8 trace_flags=01 [PlaygroundId]=804877517583753216 [DockerId]=804877517617307648 - 回收释放url资源:804877626207559680"
2025-06-12T18:18:14+08:00,1749723494368265,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:18:13,403 [paas-async-78] [DEBUG] [URLResourceService] [trace_id]=758e01175b13d968c07aac04b6455fe2 span_id=585ec6c15a9acad8 trace_flags=01 [PlaygroundId]=804877517583753216 [DockerId]=804877517617307648 - 回收释放url资源:804877626291445760
java.lang.NullPointerException: Cannot invoke ""java.util.List.size()"" because the return value of ""com.taofen8.mid.kong.admin.response.RoutePageResp.getData()"" is null
	at com.taofen8.mid.kong.admin.JKongAdmin.deleteRoutesByService(JKongAdmin.java:332)
	at com.dao42.paas.service.resource.GatewayService.unbind(GatewayService.java:73)
	at com.dao42.paas.service.resource.URLResourceService.lambda$recycleURLResource$0(URLResourceService.java:222)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at com.dao42.paas.service.resource.URLResourceService.recycleURLResource(URLResourceService.java:207)
	at com.dao42.paas.service.resource.URLResourceService.recycle(URLResourceService.java:91)
	at com.dao42.paas.service.resource.URLResourceService.recycle(URLResourceService.java:79)
	at com.dao42.paas.service.docker.handler.EventsCallbackHandler.dealDieEvent(EventsCallbackHandler.java:186)
	at com.dao42.paas.service.docker.handler.EventsCallbackHandler.onNext(EventsCallbackHandler.java:100)
	at com.dao42.paas.service.docker.handler.EventsCallbackHandler.onNext(EventsCallbackHandler.java:38)
	at com.github.dockerjava.core.exec.AbstrAsyncDockerCmdExec$1.onNext(AbstrAsyncDockerCmdExec.java:41)
	at com.github.dockerjava.core.DefaultInvocationBuilder$JsonSink.accept(DefaultInvocationBuilder.java:315)
	at com.github.dockerjava.core.DefaultInvocationBuilder$JsonSink.accept(DefaultInvocationBuilder.java:298)
	at com.github.dockerjava.core.DefaultInvocationBuilder.lambda$executeAndStream$1(DefaultInvocationBuilder.java:275)
	at java.base/java.lang.Thread.run(Thread.java:831)"
2025-06-12T18:18:14+08:00,1749723494368472,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:18:13,434 [paas-async-78] [DEBUG] [URLResourceService] [trace_id]=758e01175b13d968c07aac04b6455fe2 span_id=585ec6c15a9acad8 trace_flags=01 [PlaygroundId]=804877517583753216 [DockerId]=804877517617307648 - 回收释放url资源:804877626383720448
java.lang.NullPointerException: Cannot invoke ""java.util.List.size()"" because the return value of ""com.taofen8.mid.kong.admin.response.RoutePageResp.getData()"" is null
	at com.taofen8.mid.kong.admin.JKongAdmin.deleteRoutesByService(JKongAdmin.java:332)
	at com.dao42.paas.service.resource.GatewayService.unbind(GatewayService.java:73)
	at com.dao42.paas.service.resource.URLResourceService.lambda$recycleURLResource$0(URLResourceService.java:222)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at com.dao42.paas.service.resource.URLResourceService.recycleURLResource(URLResourceService.java:207)
	at com.dao42.paas.service.resource.URLResourceService.recycle(URLResourceService.java:91)
	at com.dao42.paas.service.resource.URLResourceService.recycle(URLResourceService.java:79)
	at com.dao42.paas.service.docker.handler.EventsCallbackHandler.dealDieEvent(EventsCallbackHandler.java:186)
	at com.dao42.paas.service.docker.handler.EventsCallbackHandler.onNext(EventsCallbackHandler.java:100)
	at com.dao42.paas.service.docker.handler.EventsCallbackHandler.onNext(EventsCallbackHandler.java:38)
	at com.github.dockerjava.core.exec.AbstrAsyncDockerCmdExec$1.onNext(AbstrAsyncDockerCmdExec.java:41)
	at com.github.dockerjava.core.DefaultInvocationBuilder$JsonSink.accept(DefaultInvocationBuilder.java:315)
	at com.github.dockerjava.core.DefaultInvocationBuilder$JsonSink.accept(DefaultInvocationBuilder.java:298)
	at com.github.dockerjava.core.DefaultInvocationBuilder.lambda$executeAndStream$1(DefaultInvocationBuilder.java:275)
	at java.base/java.lang.Thread.run(Thread.java:831)"
2025-06-12T18:18:14+08:00,1749723494368711,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:18:13,453 [paas-async-78] [DEBUG] [URLResourceService] [trace_id]=758e01175b13d968c07aac04b6455fe2 span_id=585ec6c15a9acad8 trace_flags=01 [PlaygroundId]=804877517583753216 [DockerId]=804877517617307648 - 回收释放url资源:804877626467606528
java.lang.NullPointerException: Cannot invoke ""java.util.List.size()"" because the return value of ""com.taofen8.mid.kong.admin.response.RoutePageResp.getData()"" is null
	at com.taofen8.mid.kong.admin.JKongAdmin.deleteRoutesByService(JKongAdmin.java:332)
	at com.dao42.paas.service.resource.GatewayService.unbind(GatewayService.java:73)
	at com.dao42.paas.service.resource.URLResourceService.lambda$recycleURLResource$0(URLResourceService.java:222)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at com.dao42.paas.service.resource.URLResourceService.recycleURLResource(URLResourceService.java:207)
	at com.dao42.paas.service.resource.URLResourceService.recycle(URLResourceService.java:91)
	at com.dao42.paas.service.resource.URLResourceService.recycle(URLResourceService.java:79)
	at com.dao42.paas.service.docker.handler.EventsCallbackHandler.dealDieEvent(EventsCallbackHandler.java:186)
	at com.dao42.paas.service.docker.handler.EventsCallbackHandler.onNext(EventsCallbackHandler.java:100)
	at com.dao42.paas.service.docker.handler.EventsCallbackHandler.onNext(EventsCallbackHandler.java:38)
	at com.github.dockerjava.core.exec.AbstrAsyncDockerCmdExec$1.onNext(AbstrAsyncDockerCmdExec.java:41)
	at com.github.dockerjava.core.DefaultInvocationBuilder$JsonSink.accept(DefaultInvocationBuilder.java:315)
	at com.github.dockerjava.core.DefaultInvocationBuilder$JsonSink.accept(DefaultInvocationBuilder.java:298)
	at com.github.dockerjava.core.DefaultInvocationBuilder.lambda$executeAndStream$1(DefaultInvocationBuilder.java:275)
	at java.base/java.lang.Thread.run(Thread.java:831)"
2025-06-12T18:18:14+08:00,1749723494368712,paas-manager-staging-7c449c6d95-qbfh4,"2025-06-12 18:18:13,455 [docker-java-stream--1473614770] [WARN ] [EventsCallbackHandler] [trace_id]= span_id= trace_flags= [PlaygroundId]= [DockerId]= - DockerScheduling-docker-event, 容器停止，但状态不正常,id=804877517617307648,status=START_FAIL"
