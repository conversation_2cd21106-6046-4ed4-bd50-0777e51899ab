services:
  mysql:
    image: mysql:8.0  # Changed from mysql:5.7.31
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    ports:
      - 3306:3306
    environment:
      MYSQL_ROOT_PASSWORD: rd123456
      MYSQL_ROOT_HOST: '%'
    volumes:
      - ./d42paas_manager/paas_develop.schema.sql:/docker-entrypoint-initdb.d/paas_develop.schema.sql
  redis:
    image: redis:6.0.6-alpine
    ports:
      - 6379:6379
    command: redis-server --requirepass rd123456
    environment:
      REDIS_PASSWORD: rd123456
  rabbitmq:
    image: rabbitmq:3.9.9-management-alpine
    ports:
      - 5672:5672
      - 15672:15672
    environment:
      RABBITMQ_DEFAULT_USER: agent
      RABBITMQ_DEFAULT_PASS: d42agent
      RABBITMQ_DEFAULT_VHOST: dev
  kong-database:
    image: postgres:14  # Changed from postgres:9.6
    restart: always
    networks:
      - kong-net
    environment:
      POSTGRES_USER: root
      POSTGRES_DB: kong
      POSTGRES_PASSWORD: 123456
      TZ: Asia/Shanghai
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"

  # 为kong数据库初始化
  kong-migration:
    image: kong:2.6.0
    command: "kong migrations bootstrap"
    networks:
      - kong-net
    restart: on-failure
    environment:
      KONG_PG_HOST: kong-database
      KONG_DATABASE: postgres
      KONG_PG_USER: root
      KONG_PG_PASSWORD: 123456
    links:
      - kong-database
    depends_on:
      - kong-database

  # 启动kong
  kong:
    image: kong:2.6.0
    restart: always
    networks:
      - kong-net
      - shared-network
    environment:
      KONG_PG_HOST: kong-database
      KONG_DATABASE: postgres
      KONG_PG_USER: root
      KONG_PG_PASSWORD: 123456
      KONG_CASSANDRA_CONTACT_POINTS: kong-database
      KONG_PROXY_LISTEN: 0.0.0.0:8000
      KONG_PROXY_LISTEN_SSL: 0.0.0.0:8443
      KONG_ADMIN_LISTEN: 0.0.0.0:8001
      TZ: Asia/Shanghai
    depends_on:
      - kong-migration
      - kong-database
    ports:
      - "5001:8001"
      - "5000:8000"
      - "8002:8002"
      - "8443:8443"
      - "8444:8444"
      - "8222:8222"
    labels:
      - "traefik.http.routers.kong.rule=HostRegexp(`{subdomain:.+}-app.xy-huangmj.com`,`{subdomain:.+}-lsp.xy-huangmj.com`)"
      - "traefik.http.routers.kong.entrypoints=web"
      - "traefik.http.routers.kong.service=kong"
      - "traefik.http.services.kong.loadbalancer.server.port=8000"
#  dockerImage:
#    build: ./docker/dockerImage
#    image: cr.1024paas.com/app/docker
#  dockerMysqlImage:
#    build: ./docker/mysql
#    image: cr.1024paas.com/app/mysql
networks:
  kong-net:
    driver: bridge
  shared-network:
    driver: bridge 