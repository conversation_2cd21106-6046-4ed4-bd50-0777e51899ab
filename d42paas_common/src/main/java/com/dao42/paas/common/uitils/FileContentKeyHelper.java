package com.dao42.paas.common.uitils;

import com.dao42.paas.common.constants.RedisPrefix;
import org.springframework.data.util.Pair;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

public class FileContentKeyHelper {

    public static String generate(Long dockerId, String path) {
        String base64Path = Base64.getEncoder().encodeToString(
                path.getBytes(StandardCharsets.UTF_8));
        return RedisPrefix.PREFIX_FILE_CONTENT + dockerId + ":" + base64Path;
    }

    public static Pair<Long, String> getDockerIdAndPath(String fileContentKey) {
        String[] array = fileContentKey.split(":");
        Long dockerId = Long.valueOf(array[1]) ;
        String path = new String(Base64.getDecoder().decode(array[2]), StandardCharsets.UTF_8);
        return Pair.of(dockerId, path);
    }
}
