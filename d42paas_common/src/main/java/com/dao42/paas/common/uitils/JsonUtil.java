package com.dao42.paas.common.uitils;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.io.Reader;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by liuw on 2017/6/21.
 */
@Slf4j
public class JsonUtil {

    private static ObjectMapper mapper;

    private JsonUtil() {
    }

    public static <T> String pojoToJson(T pojo) {
        if(pojo == null) {
            return null;
        } else {
            try {
                String e = getMapper().writeValueAsString(pojo);
                return e;
            } catch (IOException var2) {
                log.error("pojoToJson Error", var2);
                return null;
            }
        }
    }

    public static <T> T jsonToPojo(String json, Class<T> pojoClass) {
        if(!StringUtils.hasText(json)) {
            return null;
        } else {
            try {
                return getMapper().readValue(json, pojoClass);
            } catch (IOException e) {
                e.printStackTrace();
                return null;
            }
        }
    }

    public static <T> T jsonToPojo(
        Reader src, Class<T> pojoClass) throws JsonParseException, JsonMappingException, IOException {
        return getMapper().readValue(src, pojoClass);
    }

    public static <T> T jsonToPojo(String json, TypeReference<T> valueTypeRef) {
        if(!StringUtils.hasText(json)) {
            return null;
        } else {
            try {
                return getMapper().readValue(json, valueTypeRef);
            } catch (IOException var3) {
                return null;
            }
        }
    }

    public static Map<String, Object> jsonToMap(String json) {
        if(!StringUtils.hasText(json)) {
            return null;
        } else {
            try {
                return (Map)getMapper().readValue(json, HashMap.class);
            } catch (IOException var2) {
                log.error("jsonToMap fail", var2);
                return null;
            }
        }
    }

    public static Map<String, Map<String, String>> jsonToNewMap(String json) {
        if(!StringUtils.hasText(json)) {
            return null;
        } else {
            try {
                return (Map)getMapper().readValue(json, HashMap.class);
            } catch (IOException var2) {
                return null;
            }
        }
    }

    private static ObjectMapper getMapper() {
        if(mapper != null) {
            return mapper;
        } else {
            Class var0 = JsonUtil.class;
            synchronized(JsonUtil.class) {
                if(mapper != null) {
                    return mapper;
                } else {
                    mapper = new ObjectMapper()
                        .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES,false)
                        .setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
                    return mapper;
                }
            }
        }
    }
}
