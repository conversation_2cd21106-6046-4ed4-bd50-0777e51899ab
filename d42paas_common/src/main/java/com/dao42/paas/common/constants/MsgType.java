package com.dao42.paas.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * MQ消息类型，根据重要性设置了不同的消息优先级
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum MsgType {
    /* *************** */
    /* 发送给IDE Server */
    /* *************** */

    /**
     * Playground心跳，优先级最高
     */
    PLAYGROUND_HEART_BEAT("heartbeat", 10),
    /**
     * Playground状态变更，优先级最高
     */
    CHANGE_PLAYGROUND_INFO("changePlaygroundInfo", 10),
    /**
     * Playground状态变更，优先级最高
     */
    CHANGING_PLAYGROUND("changingPlayground", 10),
    /**
     * IdeServer初始化PlaygroundItem，manger发送同步信息
     */
    PLAYGROUND_INFO("playgroundInfo", 9),
    /**
     *
     */
    PLAYGROUND_URL_RESOURCE("playgroundUrlResource", 9),
    /**
     * 激活请求/结果
     */
    ACTIVE("active", 9),
    RESTART_DOCKER("restartDocker", 9),

    /* ***************** */
    /* 发送给Docker agent */
    /* ***************** */

    /**
     * 接收到MQ_START后，为容器执行mount的结果
     */
    MOUNT_SUCCESS("mountSuccess", 9),
    CHANGE_IDE_SERVER("changeIdeServer", 9),
    ENV("env", 9),
    SWITCH_ENV("switchEnv", 8),
    RUN_UNIT_TEST("runUnitTest", 8),
    STOP_UNIT_TEST("stopUnitTest", 7),
    RUN_CMD("runCmd", 7),
    STOP_RUN_CMD("stopRunCmd", 7),

    /* ********** */
    /* manager接收 */
    /* ********** */
    MQ_START("mqStart", 0),
    DOCKER_INFO("dockerInfo", 0),
    /**
     * 容器启动后每60s会更新一次，LSP、GUI运行状态变化时会发送
     */
    DOCKER_INFO_UPDATE("updateDockerInfo", 0),
    RUN_CMD_RESULT("runCmdResult", 0),
    UNIT_TEST_RESULT("unitTestResult", 0),
    KILL_STATUS("killStatus", 0),
    /**
     * ideServer 在线人数，用于计算负载
     */
    IDE_SERVER_USERS("onlineUsers", 0),
    /**
     * IDE Server心跳，用于维持在线状态
     */
    IDE_SERVER_HEART_BEAT("ideServerHeartBeat", 0),


    /* ******************** */
    /* agent接收，manager无关 */
    /* ******************** */
    HEART_BEAT("heartbeat", 0),
    FILE_CHANGE("fileChange", 0),

    DOCKER_STATUS("dockerStatus", 0),
    CONFIG("config", 0),
    LSP_START("lspStart", 0),
    // LSP状态
    LSP_STATUS("lspStatus", 0),
    // terminal 状态
    TERMINAL_STATUS("terminalStatus", 0),
    // rag 状态
    RAG_STATUS("ragStatus", 0),
    // 心跳
    DOCKER_HEART_BEAT("dockerHeartbeat", 0),
    REFRESH_XTERM_COLS_ROWS("refreshXtermColsRows", 0),
    // config消息中配置项的名称
    CONFIG_COMPONENTS("components", 0),
    // config中是否支持debug
    CONFIG_DEBUG_SUPPORT("debugSupport", 0),
    // 端口打开
    PORT_OPEN("portOpen", 0),
    // 运行状态
    RUN_STATUS("runStatus", 0),
    DEBUG_RUN("debugRun", 0),
    DEBUG_DAP("debugDAP", 0),
    RUN("run", 0),
    STOP("stop", 0),
    TERMINAL("terminal", 0),
    CONSOLE("console", 0);

    private final String type;
    private final int priority;

    public static MsgType find(String type) {
        for (MsgType typeEnum : MsgType.values()) {
            if (typeEnum.getType().equals(type)) {
                return typeEnum;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return type;
    }
}
