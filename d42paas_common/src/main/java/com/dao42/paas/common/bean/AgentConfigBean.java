package com.dao42.paas.common.bean;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class AgentConfigBean {

    @JsonProperty("paas_docker_id")
    private Long dockerId;
    @JsonProperty("paas_playground_id")
    private Long playgroundId;
    @JsonProperty("paas_ide_server_code")
    private String ideServerCode;

    @JsonProperty("paas_mq_host")
    private String mqHost;
    @JsonProperty("paas_mq_port")
    private int mqPort;
    @JsonProperty("paas_exchange_name")
    private String exchangeName;
    @JsonProperty("paas_mq_virtual_host")
    private String mqVHost;

    @JsonProperty("paas_app_path")
    private String appPath;
    @JsonProperty("paas_file_tree_ignore")
    private String fileTreeIgnore;
    @JsonProperty("paas_shell_cmd")
    private String shellCmd;
    @JsonProperty("paas_shell_cmd_type")
    private String shellCmdType;

    // 变成非活跃的秒数
    @JsonProperty("paas_inactive_seconds")
    private Long inactiveSeconds;

    @JsonProperty("paas_language_bash_cmd")
    private String paasLanguageBashCmd;
    @JsonProperty("paas_language")
    private String language;
    @JsonProperty("paas_language_version")
    private String languageVersion;
    @JsonProperty("paas_language_package")
    private String paasLanguagePackage;
    @JsonProperty("paas_framework")
    private String framework;
    @JsonProperty("paas_framework_version")
    private String frameworkVersion;

    // 对外服务
    @JsonProperty("paas_service_port")
    private int servicePort;

    // 容器内里的代理端口号
    @JsonProperty("paas_project_web_port")
    private int paasProjectWebPort;

    @JsonProperty("paas_url")
    private String url;

    // LSP
    @JsonProperty("paas_lsp_language_id")
    private String lspLanguageId;
    @JsonProperty("paas_lsp_port")
    private Integer lspPort;

    @JsonProperty("paas_lsp_start_cmd")
    private String lspStartCmd;

    @JsonProperty("paas_debug_start_cmd")
    private String debugStartCmd;
    @JsonProperty("paas_console_start_cmd")
    private String consoleStartCmd;
    @JsonProperty("paas_lsp_url")
    private String lspURL;

    @JsonProperty("paas_ssh_url")
    private String sshURL;

    @JsonProperty("paas_rag_path")
    private String ragPath;

    @JsonProperty("paas_common_id")
    private String gptId;

    @JsonProperty("paas_resource_id")
    private String gptKey;

    @JsonProperty("paas_base_url")
    private String proxyUrl;

    @JsonProperty("paas_auth_token")
    private String authToken;

    @JsonProperty("paas_gateway_url")
    private String gatewayUrl;


    // 代码目录下，配置文件的文件名
    @JsonProperty("paas_config_file_name")
    private String configFileName;
    @JsonProperty("paas_resource_monitoring")
    private boolean resourceMonitoring;

    @JsonProperty("paas_unittest_framework_code")
    private String unittestFrameworkCode;
}
