package com.dao42.paas.common.constants;

public class RedisPrefix {

    public final static String PREFIX_FILE_CONTENT = "fileContent:";

    public final static String PREFIX_PLAYGROUND_ACTIVE = "playground:active:";

    public final static String PREFIX_DOCKER_ACTIVE = "docker:active:";

    public final static String PREFIX_MQ_WAIT = "mq:wait:";

    public final static String PREFIX_MQ_REPLY = "mq:reply:";

    public final static String PREFIX_MQ_REPLY_CONTENT = "mq:replyContent:";

    public final static String PREFIX_MQ_WAIT_DOCKER_INFO = "mq:waitDockerInfo:";

    public final static String PREFIX_DOCKER_INFO = "dockerInfo:";

    /**
     * docker容器监控信息
     */
    public static final String PREFIX_DOCKER_STATS = "docker:stats:container:";

    /**
     * 中间件容器监控信息
     */
    public static final String PREFIX_MIDDLEWARE_STATS = "docker:stats:middleware:";

    public static final String DOCKER_SERVER_MEMORY_LEFT = "dockerServer:memory";
    public static final String DOCKER_SERVER_CPU_LOAD = "dockerServer:cpu";

    /**
     * IDE Server负载情况
     */
    public static final String IDE_SERVER_LOAD = "IDEServer:load";

    public static final String IDE_SERVER_HEARTBEAT = "IDEServer:heartbeat:";

    /**
     * 弹性伸缩锁
     */
    public static final String DOCKER_SERVER_AUTO_SCALING = "dockerServer:autoScaling";

    /**
     * 待回收serverId
     */

    public static final String MACHINE_RECYCLING_ID = "machine:recycing:id";

    /**
     * Playground激活重试计数器
     */
    public static final String PLAYGROUND_ACTIVE_RETRY = "playground:active:retry:";
    /**
     * playground 心跳
     */
    public static final String PLAYGROUND_HEARTBEAT = "playground:heartbeat:";
    /**
     * playground 心跳重试计数器
     */
    public static final String PLAYGROUND_HEARTBEAT_RETRY = "playground:heartbeat:retry:";
    /**
     * ActiveCheckJob 锁
     */
    public static final String REDISSON_REDIS_ACTIVECHECKJOB_LOCK = "redisson:redis:activecheckjob";
    /**
     * CmqMessageReceiver 锁
     */
    public static final String REDISSON_REDIS_CMQMESSAGERECEIVER_LOCK = "redisson:redis:cmqmessagereceiver";
    /**
     * DockerDestroyJob 锁
     */
    public static final String REDISSON_REDIS_DOCKERDESTORYJOB_LOCK = "redisson:redis:dockerdestroyjob";
    /**
     * DockerInfoCheckJob 锁
     */
    public static final String REDISSON_REDIS_DOCKERINFOCHECKJOB_LOCK = "redisson:redis:dockerinfocheckjob";
    /**
     * DockerServerResourcesManageJob 锁
     */
    public static final String REDISSON_REDIS_DOCKERSERVERRESOURCESMANAGEJOB_LOCK = "redisson:redis:dockerserverresourcesmanagejob";
    /**
     * DockerStopJob 锁
     */
    public static final String REDISSON_REDIS_DOCKERSTOPJOB_LOCK = "redisson:redis:dockerstopjob";

    /**
     * EventsCallbackHandler 锁
     */
    public static final String REDISSON_REDIS_EVENTSCALLBACKHANDER_LOCK = "redisson:redis:eventscallbackhandler";

    /**
     * RRateLimiter 桶前缀
     */
    public static final String REDISSON_REDIS_RRATELIMITER = "redisson:redis:rrateLimiter:";

    /**
     * btrfs 桶前缀
     */
    public static final String REDISSON_REDIS_BTRFS = "redisson:redis:btrfs";

    public static final String REDISSON_REDIS_CLONE = "redisson:redis:clone";

    /**
     * CodeZone Snapshot Fork 前缀
     */
    public static final String CODE_ZONE_SNAPSHOT_FORK_POOL = "codeZoneSnapshot:fork:pool:";

    public static final String CODE_ZONE_SNAPSHOT_FORK_POOL_JOB_ADDED = "codeZoneSnapshot:fork:pool:job:added:";

    public static final String GITHUB_IMPORT_PROGRESS = "github_import:progress:";
    public static final String GITHUB_IMPORT_LOG = "github_import:log:";

    public static final String CLONE_STATUS_PENDING = "PENDING";    // 等待克隆
    public static final String CLONE_STATUS_CLONING = "CLONING";    // 克隆中
    public static final String CLONE_STATUS_COMPLETED = "COMPLETED"; // 克隆完成
    public static final String CLONE_STATUS_FAILED = "FAILED";      // 克隆失败
    
    /**
     * Git异步创建和切换分支任务状态
     */
    public static final String CHECKOUT_BRANCH_TASK = "git:async:create_and_check_branch:task:";

    public static final String CHECKOUT_BRANCH_LOCK = "git:async:create_and_check_branch:lock:";
}