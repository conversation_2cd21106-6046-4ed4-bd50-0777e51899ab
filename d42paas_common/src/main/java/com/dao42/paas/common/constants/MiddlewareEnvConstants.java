package com.dao42.paas.common.constants;

import java.util.ArrayList;
import java.util.List;

public class MiddlewareEnvConstants {

    public static String MIDDLEWARE_LOCALHOST_DEV = "127.0.0.1";
    public static String MONGO_HOST = "MONGO_HOST";
    public static String MONGO_PORT = "MONGO_PORT";
    public static String MONGO_OUTER_HOST = "MONGO_OUTER_HOST";
    public static String MONGO_OUTER_PORT = "MONGO_OUTER_PORT";
    public static String MONGO_INNER_HOST = "MONGO_INNER_HOST";
    public static String MONGO_INNER_PORT = "MONGO_INNER_PORT";
    public static String MONGO_OUTER_USER = "MONGO_OUTER_USER";
    public static String MONGO_OUTER_PASSWORD = "MONGO_OUTER_PASSWORD";
    public static String MONGO_USER = "MONGO_USER";
    public static String MONGO_PASSWORD = "MONGO_PASSWORD";
    public static String MONGO_DB = "MONGO_DB";

    public static List<String> ENV_NAME_MONGO = new ArrayList<>();

    static {
        ENV_NAME_MONGO.add(MONGO_HOST);
        ENV_NAME_MONGO.add(MONGO_PORT);
        ENV_NAME_MONGO.add(MONGO_USER);
        ENV_NAME_MONGO.add(MONGO_PASSWORD);
    }

    public static String MYSQL_HOST = "MYSQL_HOST";
    public static String MYSQL_PORT = "MYSQL_PORT";
    public static String MYSQL_OUTER_HOST = "MYSQL_OUTER_HOST";
    public static String MYSQL_OUTER_PORT = "MYSQL_OUTER_PORT";
    public static String MYSQL_INNER_HOST = "MYSQL_INNER_HOST";
    public static String MYSQL_INNER_PORT = "MYSQL_INNER_PORT";
    public static String MYSQL_OUTER_USER = "MYSQL_OUTER_USER";
    public static String MYSQL_OUTER_PASSWORD = "MYSQL_OUTER_PASSWORD";
    public static String MYSQL_USER = "MYSQL_USER";
    public static String MYSQL_PASSWORD = "MYSQL_PASSWORD";

    public static List<String> ENV_NAME_MYSQL = new ArrayList<>();

    static {
        ENV_NAME_MYSQL.add(MYSQL_HOST);
        ENV_NAME_MYSQL.add(MYSQL_PORT);
        ENV_NAME_MYSQL.add(MYSQL_USER);
        ENV_NAME_MYSQL.add(MYSQL_PASSWORD);
    }

    public static String POSTGRE_SQL_HOST = "POSTGRE_SQL_HOST";
    public static String POSTGRE_SQL_PORT = "POSTGRE_SQL_PORT";
    public static String POSTGRE_SQL_OUTER_HOST = "POSTGRE_SQL_OUTER_HOST";
    public static String POSTGRE_SQL_OUTER_PORT = "POSTGRE_SQL_OUTER_PORT";
    public static String POSTGRE_SQL_INNER_HOST = "POSTGRE_SQL_INNER_HOST";
    public static String POSTGRE_SQL_INNER_PORT = "POSTGRE_SQL_INNER_PORT";
    public static String POSTGRE_SQL_OUTER_USER = "POSTGRE_SQL_OUTER_USER";
    public static String POSTGRE_SQL_OUTER_PASSWORD = "POSTGRE_SQL_OUTER_PASSWORD";
    public static String POSTGRE_SQL_USER = "POSTGRE_SQL_USER";
    public static String POSTGRE_SQL_PASSWORD = "POSTGRE_SQL_PASSWORD";

    public static List<String> ENV_NAME_POSTGRE_SQL = new ArrayList<>();

    static {
        ENV_NAME_POSTGRE_SQL.add(POSTGRE_SQL_HOST);
        ENV_NAME_POSTGRE_SQL.add(POSTGRE_SQL_PORT);
        ENV_NAME_POSTGRE_SQL.add(POSTGRE_SQL_USER);
        ENV_NAME_POSTGRE_SQL.add(POSTGRE_SQL_PASSWORD);
    }

    public static String REDIS_HOST = "REDIS_HOST";
    public static String REDIS_PORT = "REDIS_PORT";
    public static String REDIS_OUTER_HOST = "REDIS_OUTER_HOST";
    public static String REDIS_OUTER_PORT = "REDIS_OUTER_PORT";
    public static String REDIS_OUTER_PASSWORD = "REDISCLI_OUTER_PASSWORD";
    public static String REDIS_OUTER_USER = "REDISCLI_OUTER_USER";
    public static String REDIS_INNER_HOST = "REDIS_INNER_HOST";
    public static String REDIS_INNER_PORT = "REDIS_INNER_PORT";
    public static String REDIS_PASSWORD = "REDISCLI_AUTH";

    public static List<String> ENV_NAME_REDIS = new ArrayList<>();

    static {
        ENV_NAME_REDIS.add(REDIS_HOST);
        ENV_NAME_REDIS.add(REDIS_PORT);
        ENV_NAME_REDIS.add(REDIS_PASSWORD);
    }
}


