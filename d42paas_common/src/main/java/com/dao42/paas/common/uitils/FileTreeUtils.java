package com.dao42.paas.common.uitils;

import com.dao42.paas.common.enums.FileTreeNodeType;
import com.dao42.paas.common.message.FileTreeMQMsg;
import org.springframework.data.util.Pair;

import java.io.File;
import java.util.List;

public class FileTreeUtils {

    public static FileTreeMQMsg getFileTreeNode(String path, String fileTreeIgnore) {
        // 构建忽略规则
        Pair<List<String>, List<String>> ignorePair = FileIgnoreUtil.generateIgnoreList(fileTreeIgnore);

        return getFileTreeNode(path, ignorePair);
    }

    public static FileTreeMQMsg getFileTreeNode(
            String rootPath, Pair<List<String>, List<String>> ignorePair) {
        FileTreeMQMsg fileTreeNodeData = new FileTreeMQMsg();
        fileTreeNodeData.setName(File.separator);
        fileTreeNodeData.setUri("file://" + rootPath);
        fileTreeNodeData.setType(FileTreeNodeType.DIRECTORY);
        addChildrenDir(fileTreeNodeData, rootPath, ignorePair);
        addChildrenFile(fileTreeNodeData, rootPath, ignorePair);
        return fileTreeNodeData;
    }

    private static void addChildrenDir(
            FileTreeMQMsg fileTreeNodeData, String fullPath, Pair<List<String>, List<String>> ignorePair) {
        File rootFile = new File(fullPath);
        if(rootFile.listFiles() == null) {
            return;
        }
        for(File file: rootFile.listFiles()) {
            if(file.isDirectory() && !FileIgnoreUtil.isIgnoreDir(file, ignorePair.getFirst())) {
                FileTreeMQMsg childNode = new FileTreeMQMsg();
                childNode.setType(FileTreeNodeType.DIRECTORY);
                childNode.setName(file.getName());
                childNode.setUri("file://" + file.getAbsolutePath());
                fileTreeNodeData.getChildren().add(childNode);
                addChildrenDir(childNode, fullPath + File.separator + file.getName(), ignorePair);
                addChildrenFile(childNode, fullPath + File.separator + file.getName(), ignorePair);
            }
        }
    }

    private static void addChildrenFile(
            FileTreeMQMsg fileTreeNodeData, String fullPath, Pair<List<String>, List<String>> ignorePair) {
        File rootFile = new File(fullPath);
        if(rootFile.listFiles() == null) {
            return;
        }
        for(File file: rootFile.listFiles()) {
            if(file.isFile()  && !FileIgnoreUtil.isIgnoreFile(file, ignorePair.getSecond())) {
                if(file.getName().startsWith(".")) {
                    continue;
                }
                FileTreeMQMsg childNode = new FileTreeMQMsg();
                childNode.setType(FileTreeNodeType.FILE);
                childNode.setName(file.getName());
                childNode.setUri("file://" + file.getAbsolutePath());
                fileTreeNodeData.getChildren().add(childNode);
            }
        }
    }
}
