package com.dao42.paas.common.uitils;

import org.springframework.data.util.Pair;
import org.springframework.util.StringUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class FileIgnoreUtil {

    public static Pair<List<String>, List<String>> generateIgnoreList(String fileTreeIgnore) {
        if(!StringUtils.hasText(fileTreeIgnore)) {
            return Pair.of(new ArrayList<>(), new ArrayList<>());
        }
        String[] ignoreArray = fileTreeIgnore.split(";");
        List<String> dirIgnoreList = new ArrayList<>();
        List<String> fileIgnoreList = new ArrayList<>();
        for(String s: ignoreArray) {
            if(s.endsWith("/")) {
                dirIgnoreList.add(s.substring(0, s.length() - 1));
            } else {
                fileIgnoreList.add(s);
            }
        }
        return Pair.of(dirIgnoreList, fileIgnoreList);
    }

    public static boolean isIgnoreDir(File file, List<String> dirIgnoreList) {
        // 隐藏文件不返回
        if(file.getName().startsWith(".")) {
            return true;
        }
        return dirIgnoreList.contains(file.getName());
    }

    public static boolean isIgnoreFile(File file, List<String> fileIgnoreList) {
        // 隐藏文件不返回
        if(file.getName().startsWith(".")) {
            return true;
        }

        // 文件名一致直接命中
        if(fileIgnoreList.contains(file.getName())) {
            return true;
        }

        // 处理含有通配符的规则
        List<String> ignoreList = fileIgnoreList.stream()
                .filter(s->s.contains("*")).collect(Collectors.toList());
        for(String rule: ignoreList) {
            Pattern p = Pattern.compile(rule.replace(".", "\\.").replace("*", ".+"));
            Matcher m = p.matcher(file.getName());
            if(m.find()) {
                return true;
            }
        }
        return false;
    }
}
