package com.dao42.paas.demo.utils;

import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

/**
 * AES 对称算法加密/解密工具类
 *
 * <AUTHOR>
 */
public class AESUtils {

    private static final String CIPHER_MODE="AES/ECB/PKCS5Padding";

    private static final String ENCRYPT_ALG ="AES";

    private static final String ENCODE="UTF-8";

    public static String encrypt(String value,String key) {
        try {
            SecretKeySpec skeySpec = new SecretKeySpec(key.getBytes(ENCODE), ENCRYPT_ALG);

            Cipher cipher = Cipher.getInstance(CIPHER_MODE);
            cipher.init(Cipher.ENCRYPT_MODE, skeySpec);

            byte[] encrypted = cipher.doFinal(value.getBytes());
            return Base64.encodeBase64String(encrypted);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }
    public static String decrypt(String encrypted,String key) {
        try {
            SecretKeySpec skeySpec = new SecretKeySpec(key.getBytes(ENCODE), ENCRYPT_ALG);

            Cipher cipher = Cipher.getInstance(CIPHER_MODE);
            cipher.init(Cipher.DECRYPT_MODE, skeySpec);
            byte[] original = cipher.doFinal(Base64.decodeBase64(encrypted));

            return new String(original);
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        return null;
    }
    public static void main(String[] args) {
        String originalString = "3_123_1646295664361";
        System.out.println("Original String to encrypt - " + originalString);
        String encryptedString = encrypt(originalString,"qwertyuioplkjhgf");
        System.out.println("Encrypted String - " + encryptedString);
        String decryptedString = decrypt("hDgLRpNHOvMRfF4vA/bBP0K5i2d/n0xmTYJyc2PfqQw=","qwertyuioplkjhgf");
        System.out.println("After decryption - " + decryptedString);
    }
}