package com.dao42.paas.demo.controller;

import com.dao42.paas.demo.controller.common.ApiV1Controller;
import com.dao42.paas.demo.convertor.TemplateConvertor;
import com.dao42.paas.demo.dto.TemplateDTO;
import com.dao42.paas.demo.framework.currentUser.CurrentUser;
import com.dao42.paas.demo.framework.dto.result.ListResultDTO;
import com.dao42.paas.demo.framework.dto.result.ResultDTO;
import com.dao42.paas.demo.framework.dto.result.ResultIdDTO;
import com.dao42.paas.demo.framework.exceptions.CustomRuntimeException;
import com.dao42.paas.demo.model.Template;
import com.dao42.paas.demo.model.UserInfo;
import com.dao42.paas.demo.repository.TemplateRepository;
import com.dao42.paas.demo.service.TemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;


@RequiredArgsConstructor
@ApiV1Controller
@RequestMapping("/templates")
@Slf4j
@Api(tags = "模版")
@ApiSort()
public class TemplateController {

    @Autowired
    private TemplateService templateService;
    @Autowired
    private TemplateConvertor templateConvertor;
    @Value("${password}")
    private String demoPassword;

    @GetMapping
    @ApiOperation(value = "list")
    @ApiOperationSupport(order = 1)
    public ListResultDTO<TemplateDTO> list() {
        List<Template> templates = this.templateService.list();
        return templateConvertor.toResultDTO(templates);
    }

    @GetMapping(value = "/unitTest")
    @ApiOperation(value = "unitTestList")
    @ApiOperationSupport(order = 2)
    public ListResultDTO<TemplateDTO> unitTestList() {
        List<Template> templates = this.templateService.listUnitTest();
        return templateConvertor.toResultDTO(templates);
    }

    @GetMapping(value = "/middleware")
    @ApiOperation(value = "middlewareList")
    @ApiOperationSupport(order = 3)
    public ListResultDTO<TemplateDTO> middlewareList() {
        List<Template> templates = this.templateService.listMiddleware();
        return templateConvertor.toResultDTO(templates);
    }

    @PostMapping
    @ApiOperation(value = "create")
    @ApiOperationSupport(order = 4)
    public ResultDTO<ResultIdDTO> create(@RequestParam String playgroundId,
                                         @RequestParam String name,
                                         @RequestParam String password) {
        if(!demoPassword.equals(password)){
            throw new CustomRuntimeException("No permission","无权限");
        }
        Template template = this.templateService.create(playgroundId,name);
        return ResultDTO.success(ResultIdDTO.assignment(template.getId()));
    }

    @DeleteMapping
    @ApiOperation(value = "delete")
    @ApiOperationSupport(order = 5)
    public ResultDTO<Void> delete(@CurrentUser @ApiIgnore UserInfo userInfo,
                                         @RequestParam Long id,@RequestParam String password) {
        if(!demoPassword.equals(password)){
            throw new CustomRuntimeException("No permission","无权限");
        }
        this.templateService.delete(userInfo,id);
        return ResultDTO.success();
    }
}
