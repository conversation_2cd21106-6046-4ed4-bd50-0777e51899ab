package com.dao42.paas.demo.controller;

import com.dao42.paas.demo.controller.common.ApiV1Controller;
import com.dao42.paas.demo.dto.CommitIdDTO;
import com.dao42.paas.demo.framework.currentUser.CurrentUser;
import com.dao42.paas.demo.framework.dto.result.ResultDTO;
import com.dao42.paas.demo.model.UserInfo;
import com.dao42.paas.demo.service.GitService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import springfox.documentation.annotations.ApiIgnore;


@RequiredArgsConstructor
@ApiV1Controller
@RequestMapping("/templates/{id}/git")
@Slf4j
@Api(tags = "Git")
@ApiSort()
public class GitController {

    @Autowired
    private GitService gitService;
    @GetMapping
    @ApiOperation(value = "addAndCommit")
    @ApiOperationSupport(order = 1)
    public ResultDTO<CommitIdDTO> addAndCommit(@CurrentUser @ApiIgnore UserInfo userInfo,
                                               @PathVariable Long id) {
        CommitIdDTO commitIdDTO = this.gitService.addAndCommit(id);
        return ResultDTO.success(commitIdDTO);
    }
}
