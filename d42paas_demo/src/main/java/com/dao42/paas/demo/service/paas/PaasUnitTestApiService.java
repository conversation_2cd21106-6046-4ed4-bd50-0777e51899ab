package com.dao42.paas.demo.service.paas;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dao42.paas.demo.constants.URLConstants;
import com.dao42.paas.demo.dto.RunIdDTO;
import com.dao42.paas.demo.dto.RunTestCaseDTO;
import com.dao42.paas.demo.dto.TestCaseFunDTO;
import com.dao42.paas.demo.dto.TestCaseResultDTO;
import com.dao42.paas.demo.framework.dto.result.ResultError;
import com.dao42.paas.demo.framework.exceptions.CustomRuntimeException;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

@RequiredArgsConstructor
@Slf4j
@Service
public class PaasUnitTestApiService {

    @Value("${paas.service.url}")
    private String url;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private PaasApiService paasApiService;


    public List<TestCaseFunDTO> getUnitTestList(Long playgroundId) {
        //body
        MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<>();
        //HttpEntity
        HttpEntity<MultiValueMap> requestEntity = new HttpEntity<>(requestBody, this.paasApiService.setHeader(null));
        ResponseEntity<String> responseEntity = restTemplate.exchange(
            url + URLConstants.GET_UNIT_TEST_LIST.replace("{playgroundId}", String.valueOf(playgroundId)),
            HttpMethod.GET, requestEntity, String.class);
        if (responseEntity.getBody() != null) {
            return assignmentListUnitTest(responseEntity);
        }
        return null;
    }

    public TestCaseResultDTO getResult(Long playgroundId, String runId) {
        //body
        MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<>();
        //HttpEntity
        HttpEntity<MultiValueMap> requestEntity = new HttpEntity<>(requestBody, this.paasApiService.setHeader(null));
        ResponseEntity<String> responseEntity = restTemplate.exchange(
            url + URLConstants.GET_UNIT_TEST_RESULT.replace("{playgroundId}", String.valueOf(playgroundId))
                .replace("{runId}", runId), HttpMethod.GET, requestEntity, String.class);

        TestCaseResultDTO testCaseResultDTO = new TestCaseResultDTO();
        if (responseEntity.getBody() != null) {
            ObjectMapper objectMapper = new ObjectMapper();
            testCaseResultDTO = objectMapper.convertValue(
                this.paasApiService.assignmentData(responseEntity),
                TestCaseResultDTO.class);
        }
        return testCaseResultDTO;
    }

    public RunIdDTO run(RunTestCaseDTO runTestCaseDTO) {
        Map<String, String> map = new HashMap<>();
        map.put("fileKey", runTestCaseDTO.getFileKey());
        map.put("methodName", runTestCaseDTO.getMethodName());
        RunIdDTO runIdDTO = new RunIdDTO();
        HttpEntity<Map<String, String>> request = new HttpEntity<>(map, this.paasApiService.setHeader(null));
        final ResponseEntity<String> responseEntity = this.restTemplate.postForEntity(
            url + URLConstants.RUN_UNIT_TEST.replace("{playgroundId}", runTestCaseDTO.getPlaygroundId()), request,
            String.class);
        if (responseEntity.getBody() != null) {
            ObjectMapper objectMapper = new ObjectMapper();
            runIdDTO = objectMapper.convertValue(
                this.paasApiService.assignmentData(responseEntity),
                RunIdDTO.class);
        }
        return runIdDTO;
    }

    private List<TestCaseFunDTO> assignmentListUnitTest(ResponseEntity<String> responseEntity) {
        JSONObject jsonObject = JSONObject.parseObject(responseEntity.getBody());
        ResultError error = new ResultError();
        if (jsonObject.getString("status").equals("failure")) {
            ObjectMapper objectMapper = new ObjectMapper();
            error = objectMapper.convertValue(
                jsonObject.getJSONArray("errors").get(0),
                ResultError.class);
            throw new CustomRuntimeException("request.failed",
                error.getErrmsg() != null ? error.getErrmsg() : error.getErrcode());
        }
        return JSON.parseArray(jsonObject.getString("data"), TestCaseFunDTO.class);
    }

}
