package com.dao42.paas.demo.convertor;

import com.dao42.paas.demo.dto.CodeSpaceUnitTestDTO;
import com.dao42.paas.demo.framework.dto.AbstractConvertor;
import com.dao42.paas.demo.model.CodeSpaceUnitTestFile;
import org.springframework.stereotype.Component;

@Component
public class CodeSpaceUnitTestConvertor extends AbstractConvertor<CodeSpaceUnitTestFile, CodeSpaceUnitTestDTO> {

    @Override
    public CodeSpaceUnitTestFile toModel(CodeSpaceUnitTestDTO codeSpaceDTO) {
        throw new IllegalStateException();
    }

    @Override
    public CodeSpaceUnitTestDTO toDTO(CodeSpaceUnitTestFile codeSpace, boolean forListView) {
        final CodeSpaceUnitTestDTO codeSpaceDTO = new CodeSpaceUnitTestDTO();
        codeSpaceDTO.setFileKey(codeSpace.getFileKey());
        return codeSpaceDTO;
    }
}