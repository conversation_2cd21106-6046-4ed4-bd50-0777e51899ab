package com.dao42.paas.demo.repository;

import com.dao42.paas.demo.enums.TemplateTypeEnum;
import com.dao42.paas.demo.model.Template;
import org.springframework.data.repository.CrudRepository;

import java.util.List;


public interface TemplateRepository extends CrudRepository<Template, Long> {

    List<Template> findAllByDeletedFalseAndTemplateTypeEnum(TemplateTypeEnum templateTypeEnum);

    Template findByIdAndDeletedFalse(Long id);
}
