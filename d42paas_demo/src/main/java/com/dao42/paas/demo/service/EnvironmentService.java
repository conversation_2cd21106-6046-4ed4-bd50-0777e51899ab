package com.dao42.paas.demo.service;

import com.dao42.paas.demo.dto.EnvironmentResponseDTO;
import com.dao42.paas.demo.model.UserInfo;
import com.dao42.paas.demo.service.paas.PaasApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@RequiredArgsConstructor
@Slf4j
@Service
public class EnvironmentService {

    @Autowired
    private PaasApiService paasApiService;

    @Transactional
    public List<EnvironmentResponseDTO> list(UserInfo userInfo) {
        return paasApiService.getEnvList(userInfo);
    }
}
