package com.dao42.paas.demo.dto.middleware;

import com.dao42.paas.demo.framework.dto.AbstractDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
public class MiddlewareDTO extends AbstractDTO {

    private static final long serialVersionUID = 8524458296482029813L;
    /**
     * 中间件定义ID
     */
    private Long middlewareDefineId;

    /**
     * 中间件名称
     */
    private String name;

    /**
     * 资源的env值，json形式
     * {"MYSQL_HOST":"xxx.com", "MYSQL_PORT": 3306}
     */
    @ApiModelProperty("中间件版本")
    private Map<String, Object> envMap = new HashMap<>();
}
