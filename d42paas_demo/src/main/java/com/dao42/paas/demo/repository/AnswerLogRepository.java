package com.dao42.paas.demo.repository;

import com.dao42.paas.demo.model.AnswerLog;
import com.dao42.paas.demo.model.CodeSpace;
import com.dao42.paas.demo.model.UserInfo;
import org.springframework.data.repository.CrudRepository;

import java.util.List;


public interface AnswerLogRepository extends CrudRepository<AnswerLog, Long> {

    AnswerLog findByQuestionIdAndUserInfoIdAndPlaygroundId(Long questionId, Long userId, String playgroundId);
}
