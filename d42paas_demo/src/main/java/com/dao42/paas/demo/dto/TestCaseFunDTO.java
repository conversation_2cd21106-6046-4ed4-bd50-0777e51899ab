package com.dao42.paas.demo.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.util.List;

/**
 * TestCaseFunDTO
 */
@NoArgsConstructor
@Data
@Getter
@Setter
public class TestCaseFunDTO {

    @JsonProperty("path")
    private String path;
    @JsonProperty("testCaseFunDTOList")
    private List<TestCaseFunDTOListDTO> testCaseFunDTOList;

    @NoArgsConstructor
    @Data
    public static class TestCaseFunDTOListDTO {

        @JsonProperty("name")
        private String name;
        @JsonProperty("methodName")
        private String methodName;
        @JsonProperty("displayName")
        private Object displayName;
        @JsonProperty("input")
        private Object input;
        @JsonProperty("expectedOutput")
        private Object expectedOutput;
    }
}
