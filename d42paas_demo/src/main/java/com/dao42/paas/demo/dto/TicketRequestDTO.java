package com.dao42.paas.demo.dto;

import com.dao42.paas.demo.framework.dto.AbstractDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * TicketRequestDTO
 */
@Getter
@Setter
public class TicketRequestDTO extends AbstractDTO {

    @ApiModelProperty("playgroundId")
    @NotBlank
    private String playgroundId;

    @ApiModelProperty("用户信息")
    private PaasUserInfoDTO userInfo;

    @ApiModelProperty("截止时间")
    @NotNull
    private Long tillTime;

    @ApiModelProperty("有权限的动作")
    private String actions;
}
