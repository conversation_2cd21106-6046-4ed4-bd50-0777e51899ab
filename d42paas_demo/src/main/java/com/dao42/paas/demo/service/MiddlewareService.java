package com.dao42.paas.demo.service;

import com.dao42.paas.demo.dto.IdNameDTO;
import com.dao42.paas.demo.dto.middleware.MiddlewareAddDTO;
import com.dao42.paas.demo.dto.middleware.MiddlewareDTO;
import com.dao42.paas.demo.model.CodeSpace;
import com.dao42.paas.demo.service.paas.PaasMiddlewareApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@RequiredArgsConstructor
@Slf4j
@Service
public class MiddlewareService {

    @Autowired
    private PaasMiddlewareApiService paasMiddlewareApiService;
    @Autowired
    private CodeSpaceService codeSpaceService;

    @Transactional
    public List<IdNameDTO> getTypeList() {
        return this.paasMiddlewareApiService.getTypeList();
    }

    public List<MiddlewareDTO> getList(Long codeId) {
        CodeSpace codeSpace = this.codeSpaceService.get(codeId);
        return this.paasMiddlewareApiService.getList(codeSpace.getCodeZoneId());
    }

    public void add(MiddlewareAddDTO middlewareAddDTO) {
        CodeSpace codeSpace = this.codeSpaceService.get(middlewareAddDTO.getCodeId());
        this.paasMiddlewareApiService.add(codeSpace.getCodeZoneId(),middlewareAddDTO.getTypeId());
    }

    public void delete(MiddlewareAddDTO middlewareAddDTO) {
        CodeSpace codeSpace = this.codeSpaceService.get(middlewareAddDTO.getCodeId());
        this.paasMiddlewareApiService.delete(codeSpace.getCodeZoneId(),middlewareAddDTO.getTypeId());
    }
}
