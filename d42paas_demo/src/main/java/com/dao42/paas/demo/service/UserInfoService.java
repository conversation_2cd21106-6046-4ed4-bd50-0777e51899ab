package com.dao42.paas.demo.service;

import com.dao42.paas.demo.dto.UserInfoDTO;
import com.dao42.paas.demo.model.UserInfo;
import com.dao42.paas.demo.repository.UserInfoRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

@RequiredArgsConstructor
@Slf4j
@Service
public class UserInfoService {

    private final UserInfoRepository userInfoRepository;

    @Transactional
    public String login(UserInfoDTO userInfoDTO){
        UserInfo userInfo = this.userInfoRepository.findByName(userInfoDTO.getName());
        if(userInfo == null){
            userInfo = new UserInfo();
            userInfo.setName(userInfoDTO.getName());
            userInfo.setToken(UUID.randomUUID().toString());
            this.userInfoRepository.save(userInfo);
        }
        return userInfo.getToken();
    }

}
