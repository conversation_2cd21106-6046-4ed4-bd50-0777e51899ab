package com.dao42.paas.demo.controller;

import com.dao42.paas.demo.controller.common.ApiV1Controller;
import com.dao42.paas.demo.dto.TicketDTO;
import com.dao42.paas.demo.framework.currentUser.CurrentUser;
import com.dao42.paas.demo.framework.dto.result.ResultDTO;
import com.dao42.paas.demo.model.UserInfo;
import com.dao42.paas.demo.service.TicketService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import springfox.documentation.annotations.ApiIgnore;


@RequiredArgsConstructor
@ApiV1Controller
@RequestMapping("/tickets")
@Slf4j
@Api(tags = "票")
@ApiSort()
public class TicketController {

    @Autowired
    private TicketService ticketService;

    @GetMapping
    @ApiOperation(value = "获取票")
    @ApiOperationSupport(order = 1)
    public ResultDTO<TicketDTO> ticket(@CurrentUser @ApiIgnore UserInfo userInfo,
                                       @RequestParam String playgroundId,
                                       @RequestParam(required = false) String specialNameFromQueue) {
        String ticket = this.ticketService.getTicket(userInfo,playgroundId,specialNameFromQueue);
        TicketDTO ticketDTO = new TicketDTO();
        ticketDTO.setTicket(ticket);
        return ResultDTO.success(ticketDTO);
    }
}
