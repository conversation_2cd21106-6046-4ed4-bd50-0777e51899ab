package com.dao42.paas.demo.service;

import com.dao42.paas.demo.bean.PlaygroundIdBean;
import com.dao42.paas.demo.model.UserInfo;
import com.dao42.paas.demo.service.paas.PaasApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RequiredArgsConstructor
@Slf4j
@Service
public class PlaygroundService {

    @Autowired
    private PaasApiService paasApiService;

    @Transactional
    public String create(UserInfo userInfo,String ideServerCode) {
        PlaygroundIdBean playgroundIdBean = this.paasApiService.createPlayground(userInfo,ideServerCode);
        return playgroundIdBean.getPlaygroundId();
    }

    public void download(Long id, HttpServletRequest request, HttpServletResponse response) {
        this.paasApiService.download(id,request,response);
    }
}
