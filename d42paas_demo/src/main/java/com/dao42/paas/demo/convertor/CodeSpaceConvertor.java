package com.dao42.paas.demo.convertor;

import com.dao42.paas.demo.dto.CodeSpaceDTO;
import com.dao42.paas.demo.framework.dto.AbstractConvertor;
import com.dao42.paas.demo.model.CodeSpace;
import org.springframework.stereotype.Component;

@Component
public class CodeSpaceConvertor extends AbstractConvertor<CodeSpace, CodeSpaceDTO> {

    @Override
    public CodeSpace toModel(CodeSpaceDTO codeSpaceDTO) {
        throw new IllegalStateException();
    }

    @Override
    public CodeSpaceDTO toDTO(CodeSpace codeSpace, boolean forListView) {
        final CodeSpaceDTO codeSpaceDTO = new CodeSpaceDTO();
        codeSpaceDTO.setId(codeSpace.getId());
        codeSpaceDTO.setName(codeSpace.getName());
        codeSpaceDTO.setPlaygroundId(codeSpace.getPlaygroundId());
        return codeSpaceDTO;
    }
}