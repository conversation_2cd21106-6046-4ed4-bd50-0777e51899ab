package com.dao42.paas.demo.controller;

import com.dao42.paas.demo.controller.common.ApiV1Controller;
import com.dao42.paas.demo.dto.RunIdDTO;
import com.dao42.paas.demo.dto.RunTestCaseDTO;
import com.dao42.paas.demo.dto.TestCaseFunDTO;
import com.dao42.paas.demo.dto.TestCaseResultDTO;
import com.dao42.paas.demo.framework.dto.result.ListResultDTO;
import com.dao42.paas.demo.framework.dto.result.ResultDTO;
import com.dao42.paas.demo.service.UnitTestService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;


@RequiredArgsConstructor
@ApiV1Controller
@RequestMapping("/unitTests")
@Slf4j
@Api(tags = "单元测试")
@ApiSort()
public class UnitTestController {

    @Autowired
    private final UnitTestService unitTestService;

    @GetMapping
    @ApiOperation(value = "list")
    @ApiOperationSupport(order = 1)
    public ListResultDTO<TestCaseFunDTO> list(@RequestParam Long playgroundId) {
        List<TestCaseFunDTO> testCaseFunDTOS = this.unitTestService.list(playgroundId);
        return ListResultDTO.success(testCaseFunDTOS);
    }

    @PostMapping
    @ApiOperation(value = "run")
    @ApiOperationSupport(order = 2)
    public ResultDTO<RunIdDTO> run(@RequestBody RunTestCaseDTO runTestCaseDTO) {
        RunIdDTO runIdDTO = this.unitTestService.run(runTestCaseDTO);
        return ResultDTO.success(runIdDTO);
    }

    @GetMapping(value = "/getResult")
    @ApiOperation(value = "getResult")
    @ApiOperationSupport(order = 3)
    public ResultDTO<TestCaseResultDTO> getResult(@RequestParam Long playgroundId, @RequestParam String runId) {
        TestCaseResultDTO testCaseResultDTO = this.unitTestService.getResult(playgroundId, runId);
        return ResultDTO.success(testCaseResultDTO);
    }
}
