package com.dao42.paas.demo.controller;

import com.dao42.paas.demo.controller.common.ApiV1Controller;
import com.dao42.paas.demo.convertor.UserInfoConvertor;
import com.dao42.paas.demo.dto.UserInfoDTO;
import com.dao42.paas.demo.dto.UserInfoTokenDTO;
import com.dao42.paas.demo.framework.dto.result.ListResultDTO;
import com.dao42.paas.demo.framework.dto.result.ResultDTO;
import com.dao42.paas.demo.model.UserInfo;
import com.dao42.paas.demo.repository.UserInfoRepository;
import com.dao42.paas.demo.service.UserInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;


@RequiredArgsConstructor
@ApiV1Controller
@RequestMapping("/users")
@Slf4j
@Api(tags = "用户")
@ApiSort()
public class UserInfoController {

    @Autowired
    private UserInfoService userInfoService;
    @Autowired
    private UserInfoRepository userInfoRepository;
    @Autowired
    private UserInfoConvertor userInfoConvertor;

    @PostMapping(value = "/login")
    @ApiOperation(value = "login")
    @ApiOperationSupport(order = 1)
    public ResultDTO<UserInfoTokenDTO> login(@RequestBody UserInfoDTO userInfoDTO) {
        String token = this.userInfoService.login(userInfoDTO);
        UserInfoTokenDTO userInfoTokenDTO = new UserInfoTokenDTO();
        userInfoTokenDTO.setToken(token);
        return ResultDTO.success(userInfoTokenDTO);
    }

    @GetMapping
    @ApiOperation(value = "list")
    @ApiOperationSupport(order = 2)
    public ListResultDTO<UserInfoDTO> list() {
        List<UserInfo> userInfoList = this.userInfoRepository.findAllByDeletedFalse();
        return this.userInfoConvertor.toResultDTO(userInfoList);
    }
}
