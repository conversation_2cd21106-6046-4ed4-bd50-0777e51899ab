package com.dao42.paas.demo.service.paas;

import static com.dao42.paas.demo.constants.Constant.SpecialChar.UNDER_LINE;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dao42.paas.demo.bean.CodeZoneBean;
import com.dao42.paas.demo.bean.DockerIdBean;
import com.dao42.paas.demo.bean.ForkCodeZoneBean;
import com.dao42.paas.demo.bean.PlaygroundIdBean;
import com.dao42.paas.demo.bean.ResultIdBean;
import com.dao42.paas.demo.bean.TicketBean;
import com.dao42.paas.demo.constants.URLConstants;
import com.dao42.paas.demo.dto.CommitIdDTO;
import com.dao42.paas.demo.dto.EnvironmentResponseDTO;
import com.dao42.paas.demo.dto.GitAddAndCommitDTO;
import com.dao42.paas.demo.dto.PaasUserInfoDTO;
import com.dao42.paas.demo.dto.TicketRequestDTO;
import com.dao42.paas.demo.framework.dto.result.ResultError;
import com.dao42.paas.demo.framework.exceptions.CustomRuntimeException;
import com.dao42.paas.demo.model.UserInfo;
import com.dao42.paas.demo.service.MultipartFileResource;
import com.dao42.paas.demo.utils.AESUtils;
import com.dao42.paas.demo.utils.LocalFileDownloadUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.opentelemetry.api.baggage.Baggage;
import io.opentelemetry.api.trace.Span;
import java.io.File;
import java.io.IOException;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriComponentsBuilder;

@RequiredArgsConstructor
@Slf4j
@Service
public class PaasApiService {

    @Value("${paas.service.url}")
    private String DOMAIN;
    @Value("${paas.code}")
    private String code;
    @Value("${paas.secret}")
    private String secret;
    @Value("${paas.ticket.time}")
    private Long effectiveTime;
    @Autowired
    private RestTemplate restTemplate;

    @Transactional
    public ForkCodeZoneBean fork(Map<String, String> map, UserInfo userInfo) {
        ForkCodeZoneBean codeZoneBean = new ForkCodeZoneBean();
        HttpEntity<Map<String, String>> request = new HttpEntity<>(map, setHeader(userInfo));
        final ResponseEntity<String> responseEntity = this.restTemplate.postForEntity(
            DOMAIN + URLConstants.FORK_CODE_ZONE, request, String.class);
        if (responseEntity.getBody() != null) {
            ObjectMapper objectMapper = new ObjectMapper();
            codeZoneBean = objectMapper.convertValue(
                assignmentData(responseEntity),
                ForkCodeZoneBean.class);
        }
        return codeZoneBean;
    }

    public PlaygroundIdBean createPlayground(UserInfo userInfo, String ideServerCode) {
        Map<String, String> map = new HashMap<>();
        PlaygroundIdBean playgroundIdBean = new PlaygroundIdBean();
        HttpEntity<Map<String, String>> request = new HttpEntity<>(map, setHeader(userInfo));
        String param = "";
        if (ideServerCode != null) {
            param = "?ideServerCode=" + ideServerCode;
        }
        final ResponseEntity<String> responseEntity = this.restTemplate.postForEntity(
            DOMAIN + URLConstants.CREATE_PLAYGROUND + param, request, String.class);
        if (responseEntity.getBody() != null) {
            ObjectMapper objectMapper = new ObjectMapper();
            playgroundIdBean = objectMapper.convertValue(
                assignmentData(responseEntity),
                PlaygroundIdBean.class);
        }
        return playgroundIdBean;
    }

    public String getPlayground(Map<String, String> map,UserInfo userInfo,String ideServerCode) {
        ResultIdBean resultIdBean = new ResultIdBean();
        //body
        MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<>();
        // HttpEntity
        HttpEntity<MultiValueMap> requestEntity =
                new HttpEntity<>(requestBody, setHeader(userInfo));
        String param = "";
        if (ideServerCode != null) {
            param = "?ideServerCode=" + ideServerCode;
        }
        final ResponseEntity<String> responseEntity = this.restTemplate.exchange(
            DOMAIN + URLConstants.CODE_BIND_PLAYGROUND + param, HttpMethod.GET, requestEntity, String.class, map);
        if (responseEntity.getBody() != null) {
            ObjectMapper objectMapper = new ObjectMapper();
            resultIdBean = objectMapper.convertValue(
                assignmentData(responseEntity),
                ResultIdBean.class);
        }
        return String.valueOf(resultIdBean.getId());
    }

    public String getTicket(UserInfo userInfo, String playgroundId,String specialNameFromQueue) {
        TicketRequestDTO ticketRequestDTO = new TicketRequestDTO();
        ticketRequestDTO.setPlaygroundId(String.valueOf(playgroundId));
        ticketRequestDTO.setTillTime(System.currentTimeMillis() + effectiveTime);
        PaasUserInfoDTO paasUserInfoDTO = new PaasUserInfoDTO();
        paasUserInfoDTO.setName(userInfo.getName());
        paasUserInfoDTO.setUserId(String.valueOf(userInfo.getId()));
        paasUserInfoDTO.setSpecialNameFromQueue(specialNameFromQueue);
        ticketRequestDTO.setUserInfo(paasUserInfoDTO);
        TicketBean ticketBean = new TicketBean();
        HttpEntity<TicketRequestDTO> request =
                new HttpEntity<>(ticketRequestDTO, setHeader(userInfo));
        final ResponseEntity<String> responseEntity = this.restTemplate.postForEntity(DOMAIN + URLConstants.GET_TICKET,
            request, String.class);
        if (responseEntity.getBody() != null) {
            ObjectMapper objectMapper = new ObjectMapper();
            ticketBean = objectMapper.convertValue(
                assignmentData(responseEntity),
                TicketBean.class);
        }
        return String.valueOf(ticketBean.getTicket());
    }

    public List<EnvironmentResponseDTO> getEnvList(UserInfo userInfo) {
        //body
        MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<>();
        // HttpEntity
        HttpEntity<MultiValueMap> requestEntity =
                new HttpEntity<>(requestBody, setHeader(userInfo));
        ResponseEntity<String> responseEntity = restTemplate.exchange(DOMAIN + URLConstants.GET_ENV_LIST,
            HttpMethod.GET, requestEntity, String.class);
        if (responseEntity.getBody() != null) {
            return assignmentList(responseEntity);
        }
        return null;
    }

    public CommitIdDTO gitAddAndCommitId(String id) {
        GitAddAndCommitDTO gitAddAndCommitDTO = new GitAddAndCommitDTO();
        gitAddAndCommitDTO.setMessage("feat");
        CommitIdDTO commitIdDTO = new CommitIdDTO();
        HttpEntity<GitAddAndCommitDTO> request = new HttpEntity<>(gitAddAndCommitDTO, setHeader(null));
        final ResponseEntity<String> responseEntity = this.restTemplate.postForEntity(
            DOMAIN + URLConstants.GIT_ADD_AND_COMMIT.replace("{id}", id), request, String.class);
        if (responseEntity.getBody() != null) {
            ObjectMapper objectMapper = new ObjectMapper();
            commitIdDTO = objectMapper.convertValue(
                assignmentData(responseEntity),
                CommitIdDTO.class);
        }
        return commitIdDTO;
    }

    public String createCodeZone(UserInfo userInfo, Long envId) {
        Map<String, String> map = new HashMap<>();
        map.put("environmentVerId", String.valueOf(envId));
        map.put("startCmd", "startCmd");
        CodeZoneBean codeZoneBean = new CodeZoneBean();
        HttpEntity<Map<String, String>> request = new HttpEntity<>(map, setHeader(userInfo));
        final ResponseEntity<String> responseEntity = this.restTemplate.postForEntity(
            DOMAIN + URLConstants.CREATE_CODE_ZONE, request, String.class);
        if (responseEntity.getBody() != null) {
            ObjectMapper objectMapper = new ObjectMapper();
            codeZoneBean = objectMapper.convertValue(
                assignmentData(responseEntity),
                CodeZoneBean.class);
        }
        return codeZoneBean.getId();
    }

    public void importFile(MultipartFile file, String codeZoneId) {
        ByteArrayResource resource = null;
        try {
            resource = new MultipartFileResource(file);
        } catch (IOException e) {
            throw new CustomRuntimeException("import.fail", "导入失败");
        }
        MultiValueMap<String, Object> param = new LinkedMultiValueMap<>();
        param.add("file", resource);
        HttpEntity<MultiValueMap> requestEntity = new HttpEntity<>(param, setHeader(null));
        final ResponseEntity<String> responseEntity = this.restTemplate.postForEntity(
            DOMAIN + URLConstants.IMPORT_CODE_ZONE.replace("{id}", codeZoneId), requestEntity, String.class);
        if (responseEntity.getBody() != null) {
            assignmentData(responseEntity);
        }
    }


    public void download(Long playgroundId, HttpServletRequest request, HttpServletResponse response) {
        MultiValueMap<String, Object> param = new LinkedMultiValueMap<>();
        HttpHeaders requestHeaders = setHeader(null);
        requestHeaders.setContentType(MediaType.parseMediaType("application/zip;charset=UTF-8"));
        requestHeaders.setAccept(Collections.singletonList(MediaType.parseMediaType("application/zip;charset=UTF-8")));

        HttpEntity<MultiValueMap> requestEntity = new HttpEntity<>(param, setHeader(null));
        final Resource resource = this.restTemplate.exchange(
            DOMAIN + URLConstants.DOWNLOAD_FILE.replace("{id}", String.valueOf(playgroundId)),
            HttpMethod.GET, requestEntity, Resource.class).getBody();
        if (resource != null) {
            try {
                File tmpFile = new File("tmp-" + System.currentTimeMillis());
                FileUtils.copyToFile(resource.getInputStream(), tmpFile);
                LocalFileDownloadUtil.breakPointDownload(request, response, resource.getFilename(), tmpFile);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public String chooseCodeZoneSnapShot(UserInfo userInfo, String codeZoneSnapshotId, Long playgroundId,
        String defaultOpenFile) {
        Map<String, String> map = new HashMap<>();
        DockerIdBean dockerIdBean = new DockerIdBean();

        // Query parameters
        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(DOMAIN + URLConstants.BIND_CODEZONE_SNAPSHOT)
            // Add query parameter
            .queryParam("defaultOpenFile", defaultOpenFile);
        // URI (URL) parameters
        Map<String, String> urlParams = new HashMap<>(2);
        urlParams.put("playgroundId", String.valueOf(playgroundId));
        urlParams.put("codeZoneSnapshotId", codeZoneSnapshotId);

        HttpEntity<Map<String, String>> request = new HttpEntity<>(map, setHeader(userInfo));
        final ResponseEntity<String> responseEntity = restTemplate.postForEntity(
            builder.buildAndExpand(urlParams).toUri(), request, String.class);
        if (responseEntity.getBody() != null) {
            ObjectMapper objectMapper = new ObjectMapper();
            dockerIdBean = objectMapper.convertValue(
                assignmentData(responseEntity),
                DockerIdBean.class);
        }
        return dockerIdBean.getDockerId();
    }

    public void chooseDocker(UserInfo userInfo, String dockerId, Long playgroundId, String codeZoneSnapshotId,
        String defaultOpenFile) {
        Map<String, String> map = new HashMap<>();

        // Query parameters
        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(DOMAIN + URLConstants.BIND_DOCKER)
            // Add query parameter
            .queryParam("defaultOpenFile", defaultOpenFile);
        // URI (URL) parameters
        Map<String, String> urlParams = new HashMap<>(3);
        urlParams.put("playgroundId", String.valueOf(playgroundId));
        urlParams.put("codeZoneSnapshotId", codeZoneSnapshotId);
        urlParams.put("dockerContainerId", dockerId);

        HttpEntity<Map<String, String>> request = new HttpEntity<>(map, setHeader(userInfo));
        final ResponseEntity<String> responseEntity = restTemplate.postForEntity(
            builder.buildAndExpand(urlParams).toUri(), request, String.class);
        if (responseEntity.getBody() != null) {

        }
    }

    public void deleteCodeZone(UserInfo userInfo,String codeZoneId) {
        //body
        MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<>();
        // HttpEntity
        HttpEntity<MultiValueMap> requestEntity =
                new HttpEntity<>(requestBody, setHeader(userInfo));
        final ResponseEntity<String> responseEntity = this.restTemplate.exchange(
            DOMAIN + URLConstants.DELETE_CODE_ZONE.replace("{id}", codeZoneId), HttpMethod.DELETE, requestEntity,
            String.class);
        if (responseEntity.getBody() != null) {
        }
    }

    public HttpHeaders setHeader(UserInfo userInfo) {
        if (userInfo == null) {
            userInfo = new UserInfo();
        }
        Span span = Span.current();
        span.setAttribute("paas.tenant.code", code);
        span.setAttribute("demo.user.id", String.valueOf(userInfo.getId()));
        span.setAttribute("demo.user.name", userInfo.getName());
        // 演示通过baggage注入额外属性
        Baggage.builder()
                .put("baggage.demo.user.id", String.valueOf(userInfo.getId()))
                .build()
                .makeCurrent();
        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.add("tenantCode", code);
        requestHeaders.add("userId", String.valueOf(userInfo.getId()));
        String nonce = getRandomString(6);
        requestHeaders.add("nonce", nonce);
        String timestamp = getTimestamp();
        requestHeaders.add("timestamp", timestamp);
        String token = getToken(code,nonce,timestamp);
        requestHeaders.add("token", token);
        return requestHeaders;
    }

    private String getToken(String tenantCode, String nonce, String timestamp) {
        String str = tenantCode+ UNDER_LINE + nonce + UNDER_LINE + timestamp;
        return AESUtils.encrypt(str,secret);
    }

    private String getTimestamp() {
        // 获取一个月后的时间戳
        final Calendar start = Calendar.getInstance();
        start.setTime(new Date());
        start.set(Calendar.MONTH, start.get(Calendar.MONTH) + 1);
        return String.valueOf(start.getTime().getTime());
    }

    private String getRandomString(int length){
        String str="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        Random random=new Random();
        StringBuffer sb=new StringBuffer();
        for(int i=0;i<length;i++){
            int number=random.nextInt(62);
            sb.append(str.charAt(number));
        }
        return sb.toString();
    }

    public Object assignmentData(ResponseEntity<String> responseEntity) {
        JSONObject jsonObject = JSONObject.parseObject(responseEntity.getBody());
        ResultError error = new ResultError();
        if(jsonObject.getString("status").equals("failure")){
            ObjectMapper objectMapper = new ObjectMapper();
            error = objectMapper.convertValue(
                    jsonObject.getJSONArray("errors").get(0),
                    ResultError.class);
            throw new CustomRuntimeException("request.failed",error.getErrmsg() != null ?error.getErrmsg():error.getErrcode());
        }
        return JSON.parseObject(jsonObject.getString("data"), Object.class);
    }

    private List<EnvironmentResponseDTO> assignmentList(ResponseEntity<String> responseEntity) {
        JSONObject jsonObject =JSONObject.parseObject(responseEntity.getBody());
        ResultError error = new ResultError();
        if(jsonObject.getString("status").equals("failure")){
            ObjectMapper objectMapper = new ObjectMapper();
            error = objectMapper.convertValue(
                    jsonObject.getJSONArray("errors").get(0),
                    ResultError.class);
            throw new CustomRuntimeException("request.failed",error.getErrmsg() != null ?error.getErrmsg():error.getErrcode());
        }
        return JSON.parseArray(jsonObject.getString("data"), EnvironmentResponseDTO.class);
    }
}
