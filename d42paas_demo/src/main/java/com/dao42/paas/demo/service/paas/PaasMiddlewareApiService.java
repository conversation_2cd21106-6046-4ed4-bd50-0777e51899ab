package com.dao42.paas.demo.service.paas;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dao42.paas.demo.constants.URLConstants;
import com.dao42.paas.demo.dto.IdNameDTO;
import com.dao42.paas.demo.dto.middleware.MiddlewareDTO;
import com.dao42.paas.demo.framework.dto.result.ResultError;
import com.dao42.paas.demo.framework.exceptions.CustomRuntimeException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.List;

@RequiredArgsConstructor
@Slf4j
@Service
public class PaasMiddlewareApiService {

    @Value("${paas.service.url}")
    private String url;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private PaasApiService paasApiService;


    public List<IdNameDTO> getTypeList() {
        //body
        MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<>();
        //HttpEntity
        HttpEntity<MultiValueMap> requestEntity = new HttpEntity<>(requestBody, this.paasApiService.setHeader(null));
        ResponseEntity<String> responseEntity = restTemplate.exchange(url+ URLConstants.GET_MIDDLEWARE_LIST, HttpMethod.GET, requestEntity, String.class);
        if(responseEntity.getBody() != null){
            return assignmentList(responseEntity);
        }
        return null;
    }

    public List<MiddlewareDTO> getList(String codeZoneId) {
        //body
        MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<>();
        //HttpEntity
        HttpEntity<MultiValueMap> requestEntity = new HttpEntity<>(requestBody, this.paasApiService.setHeader(null));
        ResponseEntity<String> responseEntity = restTemplate.exchange(url+ URLConstants.GET_CODE_ZONE_MIDDLEWARE_LIST.replace("{codeZoneId}",codeZoneId), HttpMethod.GET, requestEntity, String.class);
        if(responseEntity.getBody() != null){
            return assignmentListMiddlewareDTO(responseEntity);
        }
        return null;
    }

    public void add(String codeZoneId, Long typeId) {
        //body
        MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<>();
        //HttpEntity
        HttpEntity<MultiValueMap> requestEntity = new HttpEntity<>(requestBody, this.paasApiService.setHeader(null));
        final ResponseEntity<String> responseEntity =
                this.restTemplate.postForEntity(url+ URLConstants.GET_ADD_CODE_ZONE_MIDDLEWARE_LIST
                        .replace("{codeZoneId}",codeZoneId)
                        .replace("{middlewareDefineId}",String.valueOf(typeId)), requestEntity, String.class);
        if(responseEntity.getBody() != null){
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.convertValue(
                    this.paasApiService.assignmentData(responseEntity),
                    Object.class);
        }
    }

    public void delete(String codeZoneId, Long typeId) {
        //body
        MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<>();
        //HttpEntity
        HttpEntity<MultiValueMap> requestEntity = new HttpEntity<>(requestBody, this.paasApiService.setHeader(null));
        final ResponseEntity<String> responseEntity = this.restTemplate.exchange(url+ URLConstants.GET_ADD_CODE_ZONE_MIDDLEWARE_LIST
                .replace("{codeZoneId}",codeZoneId)
                .replace("{middlewareDefineId}",String.valueOf(typeId)), HttpMethod.DELETE,requestEntity,String.class);
        if(responseEntity.getBody() != null){
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.convertValue(
                    this.paasApiService.assignmentData(responseEntity),
                    Object.class);
        }
    }
    private List<IdNameDTO> assignmentList(ResponseEntity<String> responseEntity) {
        JSONObject jsonObject =JSONObject.parseObject(responseEntity.getBody());
        ResultError error = new ResultError();
        if(jsonObject.getString("status").equals("failure")){
            ObjectMapper objectMapper = new ObjectMapper();
            error = objectMapper.convertValue(
                    jsonObject.getJSONArray("errors").get(0),
                    ResultError.class);
            throw new CustomRuntimeException("request.failed",error.getErrmsg() != null ?error.getErrmsg():error.getErrcode());
        }
        return JSON.parseArray(jsonObject.getString("data"), IdNameDTO.class);
    }
    private List<MiddlewareDTO> assignmentListMiddlewareDTO(ResponseEntity<String> responseEntity) {
        JSONObject jsonObject =JSONObject.parseObject(responseEntity.getBody());
        ResultError error = new ResultError();
        if(jsonObject.getString("status").equals("failure")){
            ObjectMapper objectMapper = new ObjectMapper();
            error = objectMapper.convertValue(
                    jsonObject.getJSONArray("errors").get(0),
                    ResultError.class);
            throw new CustomRuntimeException("request.failed",error.getErrmsg() != null ?error.getErrmsg():error.getErrcode());
        }
        return JSON.parseArray(jsonObject.getString("data"), MiddlewareDTO.class);
    }
}
