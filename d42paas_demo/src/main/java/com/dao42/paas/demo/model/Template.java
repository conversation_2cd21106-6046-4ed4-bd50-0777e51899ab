package com.dao42.paas.demo.model;

import com.dao42.paas.demo.enums.TemplateStatusEnum;
import com.dao42.paas.demo.enums.TemplateTypeEnum;
import com.dao42.paas.demo.framework.jpa.model.AbstractAuditModel;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.validation.constraints.NotBlank;

/**
 * 模版代码
 */
@Getter
@Setter
@Entity
public class Template extends AbstractAuditModel {

    private static final long serialVersionUID = 5659050418016544026L;

    /**
     * 名称
     */
    @NotBlank
    private String name;

    /**
     * playgroundId
     */
    private String playgroundId;

    /**
     * codeZoneId
     */
    @NotBlank
    private String codeZoneId;

    /**
     * commitId
     */
    private String commitId;

    /**
     * templateStatusEnum
     */
    @Enumerated(EnumType.STRING)
    private TemplateStatusEnum templateStatusEnum;

    /**
     * templateTypeEnum
     */
    @Enumerated(EnumType.STRING)
    private TemplateTypeEnum templateTypeEnum;

    /**
     * 删除
     */
    private Boolean deleted;

}
