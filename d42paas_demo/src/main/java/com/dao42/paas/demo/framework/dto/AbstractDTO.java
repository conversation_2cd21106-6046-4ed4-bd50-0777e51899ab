package com.dao42.paas.demo.framework.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

public abstract class AbstractDTO implements Serializable {

    private static final long serialVersionUID = -5828841103727023345L;

    @ApiModelProperty(value = "主键ID", position = 0)
    private String id;

    public String getId() {
        return this.id;
    }

    public void setId(final String id) {
        this.id = id;
    }

    @JsonIgnore
    public boolean isNew() {
        return this.id == null;
    }
}
