package com.dao42.paas.demo.service;

import com.dao42.paas.demo.dto.CommitIdDTO;
import com.dao42.paas.demo.enums.TemplateStatusEnum;
import com.dao42.paas.demo.model.Template;
import com.dao42.paas.demo.repository.TemplateRepository;
import com.dao42.paas.demo.service.paas.PaasApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@RequiredArgsConstructor
@Slf4j
@Service
public class GitService {

    @Autowired
    private PaasApiService paasApiService;
    @Autowired
    private TemplateRepository templateRepository;

    @Transactional
    public CommitIdDTO addAndCommit(Long id) {
        Template template = this.templateRepository.findById(id).get();
        CommitIdDTO commitIdDTO = paasApiService.gitAddAndCommitId(template.getCodeZoneId());
        template.setCommitId(commitIdDTO.getCommitId());
        template.setTemplateStatusEnum(TemplateStatusEnum.AVAILABLE);
        this.templateRepository.save(template);
        return commitIdDTO;
    }
}
