package com.dao42.paas.demo.model;


import com.dao42.paas.demo.framework.jpa.model.AbstractAuditModel;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.ManyToOne;
import javax.validation.constraints.NotBlank;

/**
 * 代码空间
 */
@Getter
@Setter
@Entity
public class CodeSpace extends AbstractAuditModel {

    /**
     * 名称
     */
    @NotBlank
    private String name;

    @ManyToOne(cascade=CascadeType.ALL)
    private Template template;

    /**
     * codeZoneId
     */
    @NotBlank
    private String codeZoneId;

    /**
     * playgroundId
     */
    private String playgroundId;

    /**
     * 创建人
     */
    @ManyToOne(cascade= CascadeType.ALL)
    private UserInfo userInfo;

    /**
     * 删除
     */
    private Boolean deleted;

}
