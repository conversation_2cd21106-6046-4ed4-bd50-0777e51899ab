package com.dao42.paas.demo.dto;

import io.swagger.annotations.ApiModelProperty;
import java.util.Map;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * RunIdDTO
 */
@Getter
@Setter
public class RunIdDTO {

    private String runId;

    private String cmd;

    private String showCmd;

    private DebugConfig debug;

    @ApiModelProperty(" true为debug false为run")
    private boolean runMode;

    @Data
    public static class DebugConfig {

        private String compile;
        private boolean support;
        private Map<String, Object> launch;
    }
}
