package com.dao42.paas.demo.framework.dto.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * ResultIdDTO
 */
@Getter
@Setter
public class ResultIdDTO {

    @ApiModelProperty("id")
    private String id;

    public static ResultIdDTO assignment(final Long id) {
        final ResultIdDTO result = new ResultIdDTO();
        result.setId(String.valueOf(id));
        return result;
    }
}
