package com.dao42.paas.demo.repository;

import com.dao42.paas.demo.model.CodeSpace;
import com.dao42.paas.demo.model.UserInfo;
import org.springframework.data.repository.CrudRepository;

import java.util.List;


public interface CodeSpaceRepository extends CrudRepository<CodeSpace, Long> {

    List<CodeSpace> findAllByUserInfoAndDeletedFalseOrderByCreatedDateDesc(UserInfo userInfo);

    List<CodeSpace> findAllByUserInfoAndTemplateIdAndDeletedFalseOrderByCreatedDateDesc(UserInfo userInfo,Long templateId);

    CodeSpace findByIdAndDeletedFalse(Long id);

    CodeSpace findByPlaygroundIdAndDeletedFalse(String playgroundId);

}
