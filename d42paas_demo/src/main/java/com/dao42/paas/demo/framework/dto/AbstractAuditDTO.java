package com.dao42.paas.demo.framework.dto;

import com.dao42.paas.demo.framework.web.audit.Auditable;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * 包含审计信息的抽象 DTO 类
 */
public abstract class AbstractAuditDTO extends AbstractDTO implements Auditable {

    private static final long serialVersionUID = 132020791921886009L;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建人", position = 100)
    private String createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", position = 101)
    private Date createdDate;

    /**
     * 修改者
     */
    @ApiModelProperty(value = "最后修改人", position = 102)
    private String lastModifiedBy;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "最后修改时间", position = 103)
    private Date lastModifiedDate;

    //////////////////////////////////////////////////
    /// Getter and Setter
    //////////////////////////////////////////////////
    @Override
    public Date getCreatedDate() {
        return this.createdDate;
    }

    public final void setCreatedDate(final Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    public Date getLastModifiedDate() {
        return this.lastModifiedDate;
    }

    public final void setLastModifiedDate(final Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }
}
