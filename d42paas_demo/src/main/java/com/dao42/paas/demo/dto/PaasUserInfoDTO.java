package com.dao42.paas.demo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * UserInfoDTO
 */
@Getter
@Setter
public class PaasUserInfoDTO {

    @ApiModelProperty("用户ID")
    @NotBlank
    private String userId;

    @NotNull
    @ApiModelProperty("姓名")
    private String name;
    private String specialNameFromQueue;

    @ApiModelProperty("手机号(与邮箱二选一，最好都传)")
    private String phoneNumber;

    @ApiModelProperty("邮箱(与手机号二选一，最好都传)")
    private String email;

}
