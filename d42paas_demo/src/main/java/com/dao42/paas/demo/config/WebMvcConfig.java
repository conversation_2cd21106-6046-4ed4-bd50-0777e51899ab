package com.dao42.paas.demo.config;

import com.dao42.paas.demo.framework.currentUser.CurrentUserArgumentResolver;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Override
    public void addArgumentResolvers(final List<HandlerMethodArgumentResolver> argumentResolvers) {
        argumentResolvers.add(this.currentUserMethodArgumentResolver());
    }



    @Bean
    public CurrentUserArgumentResolver currentUserMethodArgumentResolver() {
        return new CurrentUserArgumentResolver();
    }
}