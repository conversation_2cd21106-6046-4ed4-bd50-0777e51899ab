package com.dao42.paas.demo.service;

import com.dao42.paas.demo.model.UserInfo;
import com.dao42.paas.demo.service.paas.PaasApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@RequiredArgsConstructor
@Slf4j
@Service
public class TicketService {

    @Autowired
    private PaasApiService paasApiService;

    @Transactional
    public String getTicket(UserInfo userInfo,String playgroundId,String specialNameFromQueue) {
        return this.paasApiService.getTicket(userInfo,playgroundId,specialNameFromQueue);
    }
}
