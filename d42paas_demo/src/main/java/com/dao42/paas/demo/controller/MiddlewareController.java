package com.dao42.paas.demo.controller;

import com.dao42.paas.demo.controller.common.ApiV1Controller;
import com.dao42.paas.demo.dto.IdNameDTO;
import com.dao42.paas.demo.dto.middleware.MiddlewareAddDTO;
import com.dao42.paas.demo.dto.middleware.MiddlewareDTO;
import com.dao42.paas.demo.framework.dto.result.ListResultDTO;
import com.dao42.paas.demo.framework.dto.result.ResultDTO;
import com.dao42.paas.demo.service.MiddlewareService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RequiredArgsConstructor
@ApiV1Controller
@RequestMapping("/middlewares")
@Slf4j
@Api(tags = "中间件")
@ApiSort()
public class MiddlewareController {

    @Autowired
    private MiddlewareService middlewareService;

    @GetMapping(value = "/type")
    @ApiOperation(value = "获取PaaS支持的中间件类型列表")
    @ApiOperationSupport(order = 1)
    public ListResultDTO<IdNameDTO> getTypeList() {
        List<IdNameDTO> idNameDTOS = this.middlewareService.getTypeList();
        return ListResultDTO.success(idNameDTOS);
    }

    @GetMapping(value = "/code/{codeId}")
    @ApiOperation(value = "获取codeZone依赖的中间件列表")
    @ApiOperationSupport(order = 2)
    public ListResultDTO<MiddlewareDTO> getList(@PathVariable Long codeId) {
        List<MiddlewareDTO> middlewareDTOS = this.middlewareService.getList(codeId);
        return ListResultDTO.success(middlewareDTOS);
    }

    @PostMapping(value = "/add")
    @ApiOperation(value = "增加中间件")
    @ApiOperationSupport(order = 3)
    public ResultDTO<Void> add(@RequestBody MiddlewareAddDTO middlewareAddDTO) {
       this.middlewareService.add(middlewareAddDTO);
        return ResultDTO.success();
    }

    @DeleteMapping(value = "/delete")
    @ApiOperation(value = "删除中间件")
    @ApiOperationSupport(order = 4)
    public ResultDTO<Void> delete(@RequestBody MiddlewareAddDTO middlewareAddDTO) {
       this.middlewareService.delete(middlewareAddDTO);
        return ResultDTO.success();
    }
}
