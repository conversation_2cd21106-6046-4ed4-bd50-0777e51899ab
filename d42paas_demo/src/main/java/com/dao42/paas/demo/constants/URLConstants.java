package com.dao42.paas.demo.constants;

/**
 * url
 */
public class URLConstants {

    public static final String COMMON_PATH_V1 = "/api/v1";
    //create codeZone
    public static final String CREATE_CODE_ZONE = COMMON_PATH_V1 + "/sdk/codeZones";
    public static final String IMPORT_CODE_ZONE = COMMON_PATH_V1 + "/sdk/codeZones/{id}/importFiles";
    public static final String DOWNLOAD_FILE = COMMON_PATH_V1 + "/sdk/playgrounds/{id}/files";
    //fork codeZone
    public static final String FORK_CODE_ZONE = COMMON_PATH_V1 + "/sdk/codeZones/fork";
    public static final String CREATE_PLAYGROUND = COMMON_PATH_V1 + "/sdk/playgrounds";
    public static final String BIND_CODEZONE_SNAPSHOT =
        COMMON_PATH_V1 + "/sdk/playgrounds/{playgroundId}/codeZoneSnapshot/{codeZoneSnapshotId}";
    public static final String BIND_DOCKER = COMMON_PATH_V1
        + "/sdk/playgrounds/{playgroundId}/codeZoneSnapshot/{codeZoneSnapshotId}/dockerContainer/{dockerContainerId}";
    //add and commit
    public static final String GIT_ADD_AND_COMMIT = COMMON_PATH_V1 + "/sdk/codeZones/{id}/git/addAndCommit";

    // 获取入场劵
    public static final String GET_TICKET = COMMON_PATH_V1 + "/sdk/ticket";

    // 根据 codeZone 获取 playgroundId
    public static final String CODE_BIND_PLAYGROUND = COMMON_PATH_V1 + "/sdk/codeZones/{id}/playground";

    // 删除codeZone
    public static final String DELETE_CODE_ZONE = COMMON_PATH_V1 + "/sdk/codeZones/{id}";

    // 获取环境列表
    public static final String GET_ENV_LIST = COMMON_PATH_V1 + "/sdk/environments";

    // 获取单元测试方法列表
    public static final String GET_UNIT_TEST_LIST = COMMON_PATH_V1 + "/sdk/playgrounds/{playgroundId}/unitTests/testCases?fileKey=";
    public static final String GET_UNIT_TEST_RESULT = COMMON_PATH_V1 + "/sdk/playgrounds/{playgroundId}/unitTests/result/{runId}";
    public static final String RUN_UNIT_TEST = COMMON_PATH_V1 + "/sdk/playgrounds/{playgroundId}/unitTests/run";

    // 中间件
    public static final String GET_MIDDLEWARE_LIST = COMMON_PATH_V1 + "/sdk/middlewares";
    public static final String GET_CODE_ZONE_MIDDLEWARE_LIST = COMMON_PATH_V1 + "/sdk/codeZones/{codeZoneId}/middlewareDefines";
    public static final String GET_ADD_CODE_ZONE_MIDDLEWARE_LIST = COMMON_PATH_V1 + "/sdk/codeZones/{codeZoneId}/middlewareDefines/{middlewareDefineId}";

}
