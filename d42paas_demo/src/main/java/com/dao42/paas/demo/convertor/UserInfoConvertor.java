package com.dao42.paas.demo.convertor;

import com.dao42.paas.demo.dto.UserInfoDTO;
import com.dao42.paas.demo.framework.dto.AbstractConvertor;
import com.dao42.paas.demo.model.UserInfo;
import org.springframework.stereotype.Component;

@Component
public class UserInfoConvertor extends AbstractConvertor<UserInfo, UserInfoDTO> {

    @Override
    public UserInfo toModel(UserInfoDTO userInfoDTO) {
        throw new IllegalStateException();
    }

    @Override
    public UserInfoDTO toDTO(UserInfo userInfo, boolean forListView) {
        final UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setId(userInfo.getId());
        userInfoDTO.setName(userInfo.getName());
        return userInfoDTO;
    }
}