package com.dao42.paas.demo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * ImageResponseDTO
 */
@Getter
@Setter
public class EnvironmentResponseDTO {

    @ApiModelProperty(value = "环境名", position = 100)
    private String name;

    @ApiModelProperty(value = "语言", position = 100)
    private String language;

    @ApiModelProperty(value = "框架", position = 100)
    private String framework;

    @ApiModelProperty(value = "描述", position = 100)
    private String description;

    @ApiModelProperty(value = "版本列表", position = 100)
    private List<EnvironmentVerResponseDTO> versionList = new ArrayList<>();

}
