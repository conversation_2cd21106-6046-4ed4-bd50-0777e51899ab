package com.dao42.paas.demo.model;

import com.dao42.paas.demo.framework.jpa.model.AbstractAuditModel;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;
import javax.persistence.ManyToOne;
import javax.validation.constraints.NotBlank;

/**
 * 模版代码
 */
@Getter
@Setter
@Entity
public class TemplateUnitTestFile extends AbstractAuditModel {

    private static final long serialVersionUID = 5659050418016544026L;

    /**
     * fileKey
     */
    @NotBlank
    private String fileKey;

    /**
     * template
     */
    @ManyToOne
    private Template template;
}
