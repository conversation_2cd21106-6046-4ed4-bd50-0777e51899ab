package com.dao42.paas.demo.controller;

import com.dao42.paas.demo.controller.common.ApiV1Controller;
import com.dao42.paas.demo.dto.EnvironmentResponseDTO;
import com.dao42.paas.demo.framework.currentUser.CurrentUser;
import com.dao42.paas.demo.framework.dto.result.ListResultDTO;
import com.dao42.paas.demo.model.UserInfo;
import com.dao42.paas.demo.service.EnvironmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;


@RequiredArgsConstructor
@ApiV1Controller
@RequestMapping("/environments")
@Slf4j
@Api(tags = "环境")
@ApiSort()
public class EnvironmentController {

    @Autowired
    private EnvironmentService environmentService;
    @GetMapping
    @ApiOperation(value = "list")
    @ApiOperationSupport(order = 1)
    public ListResultDTO<EnvironmentResponseDTO> list(@CurrentUser @ApiIgnore UserInfo userInfo) {
        List<EnvironmentResponseDTO> environmentResponseDTOS = this.environmentService.list(userInfo);
        return ListResultDTO.success(environmentResponseDTOS);
    }
}
