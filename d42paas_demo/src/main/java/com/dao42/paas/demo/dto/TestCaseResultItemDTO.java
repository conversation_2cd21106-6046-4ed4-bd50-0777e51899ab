package com.dao42.paas.demo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * TestCaseResultDTO
 */
@Getter
@Setter
public class TestCaseResultItemDTO {


    @ApiModelProperty("方法名/displayName")
    private String name;

    @ApiModelProperty("成功:true,失败:false")
    private boolean success;

    @ApiModelProperty("输入")
    private String input;

    @ApiModelProperty("期望输出")
    private String expectedOutput;

    @ApiModelProperty("实际输出")
    private String actualOutput;

    @ApiModelProperty("失败原因")
    private String reason;

    @ApiModelProperty("失败详细信息")
    private String details;

}
