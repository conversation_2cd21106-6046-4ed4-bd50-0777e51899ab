package com.dao42.paas.demo.service;

import com.dao42.paas.demo.dto.ChooseQuestionDTO;
import com.dao42.paas.demo.dto.ReleaseDTO;
import com.dao42.paas.demo.framework.exceptions.CustomRuntimeException;
import com.dao42.paas.demo.model.AnswerLog;
import com.dao42.paas.demo.model.Question;
import com.dao42.paas.demo.model.UserInfo;
import com.dao42.paas.demo.repository.AnswerLogRepository;
import com.dao42.paas.demo.repository.QuestionRepository;
import com.dao42.paas.demo.service.paas.PaasApiService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@RequiredArgsConstructor
@Slf4j
@Service
public class QuestionService {

    @Autowired
    private QuestionRepository questionRepository;
    @Autowired
    private AnswerLogRepository answerLogRepository;
    @Autowired
    private PaasApiService paasApiService;

    public List<Question> list() {
        return this.questionRepository.findAllByDeletedFalse();
    }

    public void create(UserInfo userInfo, ReleaseDTO releaseDTO) {
//        Question question = new Question();
//        question.setName();
//        question.setCodeZoneSnapshotId();
    }

    public void choose(UserInfo userInfo, ChooseQuestionDTO chooseQuestionDTO) {
        AnswerLog answerLog = this.answerLogRepository.findByQuestionIdAndUserInfoIdAndPlaygroundId(
            chooseQuestionDTO.getId(), userInfo.getId(), String.valueOf(chooseQuestionDTO.getPlaygroundId()));
        Question question = this.questionRepository.findById(chooseQuestionDTO.getId())
            .orElseThrow(() -> new CustomRuntimeException("question.is.not.exist", "题目不存在"));
        // 判断是否传了默认打开文件
        String defaultOpenFile =
            StringUtils.hasText(chooseQuestionDTO.getDefaultOpenFile()) ?
                chooseQuestionDTO.getDefaultOpenFile() : question.getDefaultOpenFile();
        if (answerLog == null) {
            // 绑定CodeZoneSnapshot
            String dockerId = this.paasApiService.chooseCodeZoneSnapShot(userInfo, question.getCodeZoneSnapshotId(),
                chooseQuestionDTO.getPlaygroundId(), defaultOpenFile);
            AnswerLog answerLogNew = new AnswerLog();
            answerLogNew.setDockerId(dockerId);
            answerLogNew.setPlaygroundId(String.valueOf(chooseQuestionDTO.getPlaygroundId()));
            answerLogNew.setQuestion(question);
            answerLogNew.setUserInfo(userInfo);
            this.answerLogRepository.save(answerLogNew);
        } else {
            // 绑定DockerContainer
            this.paasApiService.chooseDocker(userInfo, answerLog.getDockerId(), chooseQuestionDTO.getPlaygroundId(),
                question.getCodeZoneSnapshotId(), defaultOpenFile);
        }
    }

}
