package com.dao42.paas.demo.model;


import com.dao42.paas.demo.framework.jpa.model.AbstractAuditModel;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.validation.constraints.NotBlank;

/**
 * 用户信息
 */
@Getter
@Setter
@Entity
public class UserInfo extends AbstractAuditModel {

    private static final long serialVersionUID = 5659050418016544026L;

    /**
     * 姓名
     */
    @NotBlank
    private String name;

    /**
     * token
     */
    @NotBlank
    @Column(unique = true)
    private String token;

    /**
     * delete
     */
    private Boolean deleted;

}
