package com.dao42.paas.demo.framework.currentUser;


import com.dao42.paas.demo.framework.exceptions.CustomRuntimeException;
import com.dao42.paas.demo.model.UserInfo;
import com.dao42.paas.demo.repository.UserInfoRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import javax.servlet.http.HttpServletRequest;

public final class CurrentUserArgumentResolver implements HandlerMethodArgumentResolver {
    @Autowired
    private CurrentUserService currentUserService;
    @Autowired
    private UserInfoRepository userInfoRepository;
    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return parameter.getParameterType().isAssignableFrom(UserInfo.class)
                && parameter.hasParameterAnnotation(CurrentUser.class);
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer, NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
        String token = webRequest.getNativeRequest(HttpServletRequest.class).getHeader("token");
        UserInfo userInfo = this.userInfoRepository.findByToken(token);
        if(userInfo == null){
            throw new CustomRuntimeException("user.is.not.exist","用户不存在，请登陆");
        }
        return this.currentUserService.loadUserByUsername(userInfo);
    }
}

