package com.dao42.paas.demo.service;

import com.dao42.paas.demo.dto.RunIdDTO;
import com.dao42.paas.demo.dto.RunTestCaseDTO;
import com.dao42.paas.demo.dto.TestCaseFunDTO;
import com.dao42.paas.demo.dto.TestCaseResultDTO;
import com.dao42.paas.demo.service.paas.PaasUnitTestApiService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@RequiredArgsConstructor
@Slf4j
@Service
public class UnitTestService {

    @Autowired
    private PaasUnitTestApiService paasUnitTestApiService;

    @Transactional
    public List<TestCaseFunDTO> list(Long playgroundId) {
        return this.paasUnitTestApiService.getUnitTestList(playgroundId);
    }

    @Transactional
    public RunIdDTO run(RunTestCaseDTO runTestCaseDTO) {
        return this.paasUnitTestApiService.run(runTestCaseDTO);
    }

    @Transactional
    public TestCaseResultDTO getResult(Long playgroundId, String runId) {
        return this.paasUnitTestApiService.getResult(playgroundId, runId);
    }
}
