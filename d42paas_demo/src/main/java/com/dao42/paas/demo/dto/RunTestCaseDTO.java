package com.dao42.paas.demo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * RunTestCaseDTO
 */
@Getter
@Setter
public class RunTestCaseDTO {

    @ApiModelProperty(value = "文件 Key",required = true)
    private String fileKey;

    @ApiModelProperty("方法名 如果方法名是null,运行全部用例")
    private String methodName;

    @ApiModelProperty("playgroundId")
    private String playgroundId;


}
