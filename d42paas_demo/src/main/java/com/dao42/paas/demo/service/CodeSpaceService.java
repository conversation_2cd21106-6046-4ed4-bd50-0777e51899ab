package com.dao42.paas.demo.service;

import com.dao42.paas.demo.bean.ForkCodeZoneBean;
import com.dao42.paas.demo.dto.CommitIdDTO;
import com.dao42.paas.demo.dto.ForkCodeSpaceDTO;
import com.dao42.paas.demo.enums.TemplateStatusEnum;
import com.dao42.paas.demo.framework.exceptions.CustomRuntimeException;
import com.dao42.paas.demo.model.CodeSpace;
import com.dao42.paas.demo.model.CodeSpaceUnitTestFile;
import com.dao42.paas.demo.model.Template;
import com.dao42.paas.demo.model.TemplateUnitTestFile;
import com.dao42.paas.demo.model.UserInfo;
import com.dao42.paas.demo.repository.CodeSpaceRepository;
import com.dao42.paas.demo.repository.CodeSpaceUnitTestRepository;
import com.dao42.paas.demo.repository.TemplateRepository;
import com.dao42.paas.demo.service.paas.PaasApiService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@RequiredArgsConstructor
@Slf4j
@Service
public class CodeSpaceService {

    @Autowired private CodeSpaceRepository codeSpaceRepository;
    @Autowired private CodeSpaceUnitTestRepository codeSpaceUnitTestRepository;
    @Autowired private TemplateService templateService;
    @Autowired private GitService gitService;
    @Autowired private TemplateRepository templateRepository;
    @Autowired private PaasApiService paasApiService;

    @Transactional
    public String fork(UserInfo userInfo, ForkCodeSpaceDTO forkCodeSpaceDTO, String ideServerCode) {
        Template template = this.templateService.get(forkCodeSpaceDTO.getTemplateId());
        List<TemplateUnitTestFile> templateUnitTestFileList =
                this.templateService.getUnitTestFile(template);
        if (TemplateStatusEnum.UNAVAILABLE.equals(template.getTemplateStatusEnum())) {
            throw new CustomRuntimeException("un.available", "模版不可用,没有初始化，请调用gitCommit");
        }
        CommitIdDTO commitIdDTO = paasApiService.gitAddAndCommitId(template.getCodeZoneId());
        template.setCommitId(commitIdDTO.getCommitId());
        template.setTemplateStatusEnum(TemplateStatusEnum.AVAILABLE);
        this.templateRepository.save(template);
        this.gitService.addAndCommit(forkCodeSpaceDTO.getTemplateId());
        CodeSpace codeSpace = new CodeSpace();
        codeSpace.setDeleted(false);
        codeSpace.setName(forkCodeSpaceDTO.getName());
        codeSpace.setUserInfo(userInfo);
        codeSpace.setTemplate(template);

        Map<String, String> mapFork = new HashMap<>();
        mapFork.put("codeZoneId", template.getCodeZoneId());
        mapFork.put("commitId", template.getCommitId());
        ForkCodeZoneBean forkCodeZoneBean = this.paasApiService.fork(mapFork, userInfo);
        codeSpace.setCodeZoneId(String.valueOf(forkCodeZoneBean.getId()));
        Map<String, String> mapGetPlayground = new HashMap<>();
        mapGetPlayground.put("id", codeSpace.getCodeZoneId());
        String playgroundId =
                this.paasApiService.getPlayground(mapGetPlayground, userInfo, ideServerCode);
        codeSpace.setPlaygroundId(playgroundId);
        this.codeSpaceRepository.save(codeSpace);
        List<CodeSpaceUnitTestFile> codeSpaceUnitTestFiles = new ArrayList<>();
        templateUnitTestFileList.forEach(
                item -> {
                    CodeSpaceUnitTestFile codeSpaceUnitTestFile = new CodeSpaceUnitTestFile();
                    codeSpaceUnitTestFile.setCodeSpace(codeSpace);
                    codeSpaceUnitTestFile.setFileKey(item.getFileKey());
                    codeSpaceUnitTestFiles.add(codeSpaceUnitTestFile);
                });
        this.codeSpaceUnitTestRepository.saveAll(codeSpaceUnitTestFiles);
        return playgroundId;
    }

    @Transactional
    public List<CodeSpace> getList(UserInfo userInfo, Long templateId) {
        if (templateId == null) {
            return this.codeSpaceRepository.findAllByUserInfoAndDeletedFalseOrderByCreatedDateDesc(
                    userInfo);
        } else {
            return this.codeSpaceRepository
                    .findAllByUserInfoAndTemplateIdAndDeletedFalseOrderByCreatedDateDesc(
                            userInfo, templateId);
        }
    }

    public CodeSpace get(Long id) {
        CodeSpace codeSpace = this.codeSpaceRepository.findByIdAndDeletedFalse(id);
        if (codeSpace == null) {
            throw new CustomRuntimeException("codeSpace.is.not.exist", "代码空间不存在");
        }
        return codeSpace;
    }

    @Transactional
    public void delete(UserInfo userInfo, Long id) {
        CodeSpace codeSpace = this.get(id);
        if (!codeSpace.getUserInfo().equals(userInfo)) {
            throw new CustomRuntimeException("no.permission", "无权限");
        }
        codeSpace.setDeleted(true);
        this.codeSpaceRepository.save(codeSpace);
        this.paasApiService.deleteCodeZone(userInfo, codeSpace.getCodeZoneId());
    }

    public CodeSpace getByPlaygroundId(String playgroundId) {
        return this.codeSpaceRepository.findByPlaygroundIdAndDeletedFalse(playgroundId);
    }
}
