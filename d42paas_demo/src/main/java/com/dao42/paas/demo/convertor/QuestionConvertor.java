package com.dao42.paas.demo.convertor;

import com.dao42.paas.demo.dto.QuestionDTO;
import com.dao42.paas.demo.framework.dto.AbstractConvertor;
import com.dao42.paas.demo.model.Question;
import org.springframework.stereotype.Component;

@Component
public class QuestionConvertor extends AbstractConvertor<Question, QuestionDTO> {

    @Override
    public Question toModel(QuestionDTO questionDTO) {
        throw new IllegalStateException();
    }

    @Override
    public QuestionDTO toDTO(Question question, boolean forListView) {
        final QuestionDTO questionDTO = new QuestionDTO();
        questionDTO.setId(question.getId());
        questionDTO.setName(question.getName());
        return questionDTO;
    }
}