package com.dao42.paas.demo.config;

import com.dao42.paas.demo.controller.common.ApiV1Controller;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class PathConfig implements WebMvcConfigurer {
    @Override
    public void configurePathMatch(PathMatchConfigurer configurer) {
        configurer
                .addPathPrefix("/demo/api/v1", c -> c.isAnnotationPresent(ApiV1Controller.class));
    }
}
