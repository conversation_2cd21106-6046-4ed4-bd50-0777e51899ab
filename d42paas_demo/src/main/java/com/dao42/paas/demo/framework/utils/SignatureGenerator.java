package com.dao42.paas.demo.framework.utils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.NoSuchAlgorithmException;
import java.util.*;

/**
 * 根据请求URI、接口认证密钥和一次性请求id生成请求sign
 */
public class SignatureGenerator {

    /**
     * @param nonce 请求的uri
     * @param tenantSecret CM2发放的接口认证密钥
     * @param timestamp 一次性请求id
     * @return 生成的请求sign
     * @throws NoSuchAlgorithmException
     * @throws UnsupportedEncodingException
     */
    public static String generate(final String nonce, final String tenantCode, final String tenantSecret, final String timestamp)
            throws NoSuchAlgorithmException, UnsupportedEncodingException {
        final Map<String, String> params = new HashMap<String, String>();
        params.put("tenantCode", tenantCode);
        params.put("timestamp", timestamp);

        //对参数按名称排序(升序)
        final List<Map.Entry<String, String>> parameters = new LinkedList<Map.Entry<String, String>>(params.entrySet());
        Collections.sort(parameters, new Comparator<Map.Entry<String, String>>() {
            @Override
            public int compare(final Map.Entry<String, String> o1, final Map.Entry<String, String> o2) {
                return o1.getKey().compareTo(o2.getKey());
            }
        });

        //形成参数字符串, 并把SecretKey加在末尾（salt）
        final StringBuilder sb = new StringBuilder();
        sb.append(nonce).append("_");
        for (final Map.Entry<String, String> param : parameters) {
            sb.append(param.getKey()).append("=").append(param.getValue()).append("_");
        }
        sb.append(tenantSecret);

        final String baseString = URLEncoder.encode(sb.toString(), "UTF-8");
        return MD5Util.md5(baseString);
    }

    public static void main(final String[] args) throws UnsupportedEncodingException, NoSuchAlgorithmException {
        final String sign = SignatureGenerator.generate("/a/studentSignIn/s001", "106834927504028223118430562587",
                "ac4dd2ec946c97e2dd52ca789ecbd52d", "1531275306271");
        System.out.println(sign);
    }
}
