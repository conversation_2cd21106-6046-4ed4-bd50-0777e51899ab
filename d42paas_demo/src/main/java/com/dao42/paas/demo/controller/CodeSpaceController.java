package com.dao42.paas.demo.controller;

import com.dao42.paas.demo.controller.common.ApiV1Controller;
import com.dao42.paas.demo.convertor.CodeSpaceConvertor;
import com.dao42.paas.demo.convertor.CodeSpaceUnitTestConvertor;
import com.dao42.paas.demo.dto.CodeSpaceDTO;
import com.dao42.paas.demo.dto.CodeSpaceUnitTestDTO;
import com.dao42.paas.demo.dto.ForkCodeSpaceDTO;
import com.dao42.paas.demo.dto.PlaygroundIdDTO;
import com.dao42.paas.demo.framework.currentUser.CurrentUser;
import com.dao42.paas.demo.framework.dto.result.ListResultDTO;
import com.dao42.paas.demo.framework.dto.result.ResultDTO;
import com.dao42.paas.demo.model.CodeSpace;
import com.dao42.paas.demo.model.CodeSpaceUnitTestFile;
import com.dao42.paas.demo.model.UserInfo;
import com.dao42.paas.demo.repository.CodeSpaceUnitTestRepository;
import com.dao42.paas.demo.service.CodeSpaceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;


@RequiredArgsConstructor
@ApiV1Controller
@RequestMapping("/codes")
@Slf4j
@Api(tags = "代码")
@ApiSort()
public class CodeSpaceController {

    @Autowired
    private CodeSpaceService codeSpaceService;
    @Autowired
    private CodeSpaceUnitTestRepository codeSpaceUnitTestRepository;
    @Autowired
    private CodeSpaceConvertor codeSpaceConvertor;
    @Autowired
    private CodeSpaceUnitTestConvertor codeSpaceUnitTestConvertor;

    @PostMapping(value = "/fork")
    @ApiOperation(value = "fork")
    @ApiOperationSupport(order = 1)
    public ResultDTO<PlaygroundIdDTO> fork(@CurrentUser @ApiIgnore UserInfo userInfo,
                                           @RequestParam(required = false) @ApiIgnore String ideServerCode,
                                           @RequestBody ForkCodeSpaceDTO forkCodeSpaceDTO) {
        String playgroundId = this.codeSpaceService.fork(userInfo,forkCodeSpaceDTO,ideServerCode);
        PlaygroundIdDTO playgroundIdDTO = new PlaygroundIdDTO();
        playgroundIdDTO.setPlaygroundId(playgroundId);
        return ResultDTO.success(playgroundIdDTO);
    }

    @GetMapping
    @ApiOperation(value = "codeSpaceList")
    @ApiOperationSupport(order = 2)
    public ListResultDTO<CodeSpaceDTO> list(@CurrentUser @ApiIgnore UserInfo userInfo, @RequestParam(required = false) Long templateId) {
        List<CodeSpace> codeSpaceList = this.codeSpaceService.getList(userInfo,templateId);
        return this.codeSpaceConvertor.toResultDTO(codeSpaceList);
    }

    @GetMapping(value = "/playground/{playgroundId}/unitTestList")
    @ApiOperation(value = "codeSpaceUnitTestList")
    @ApiOperationSupport(order = 2)
    public ListResultDTO<CodeSpaceUnitTestDTO> unitTestList(@PathVariable String playgroundId) {
        CodeSpace codeSpace = this.codeSpaceService.getByPlaygroundId(playgroundId);
        List<CodeSpaceUnitTestFile> codeSpaceList = this.codeSpaceUnitTestRepository.findAllByCodeSpace(codeSpace);
        return this.codeSpaceUnitTestConvertor.toResultDTO(codeSpaceList);
    }

    @DeleteMapping(value = "/{id}")
    @ApiOperation(value = "delete")
    @ApiOperationSupport(order = 3)
    public ResultDTO<Void> delete(@CurrentUser @ApiIgnore UserInfo userInfo,
                                  @PathVariable Long id) {
        this.codeSpaceService.delete(userInfo,id);
        return ResultDTO.success();
    }
}
