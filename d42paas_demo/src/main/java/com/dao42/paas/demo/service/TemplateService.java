package com.dao42.paas.demo.service;

import com.dao42.paas.demo.dto.CommitIdDTO;
import com.dao42.paas.demo.enums.TemplateStatusEnum;
import com.dao42.paas.demo.enums.TemplateTypeEnum;
import com.dao42.paas.demo.framework.exceptions.CustomRuntimeException;
import com.dao42.paas.demo.model.CodeSpace;
import com.dao42.paas.demo.model.Template;
import com.dao42.paas.demo.model.TemplateUnitTestFile;
import com.dao42.paas.demo.model.UserInfo;
import com.dao42.paas.demo.repository.CodeSpaceRepository;
import com.dao42.paas.demo.repository.TemplateRepository;
import com.dao42.paas.demo.repository.TemplateUnitTestRepository;
import com.dao42.paas.demo.service.paas.PaasApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RequiredArgsConstructor
@Slf4j
@Service
public class TemplateService {

    @Autowired
    private final TemplateRepository templateRepository;
    @Autowired
    private final TemplateUnitTestRepository templateUnitTestFileRepository;
    @Autowired
    private final PaasApiService paasApiService;
    @Autowired
    private final GitService gitService;
    @Autowired
    private final CodeSpaceRepository codeSpaceRepository;


    public List<Template> list(){
        return this.templateRepository.findAllByDeletedFalseAndTemplateTypeEnum(TemplateTypeEnum.BASE);
    }

    public Template get(Long id) {
        final Template template = this.templateRepository.findByIdAndDeletedFalse(id);
        if(template == null){
            throw new CustomRuntimeException("template.is.not.exist","模版不存在");
        }
        return template;
    }

    public List<TemplateUnitTestFile> getUnitTestFile(Template template) {
        return this.templateUnitTestFileRepository.findAllByTemplate(template);
    }

    @Transactional
    public Template create(String playgroundId,String name) {
        Template template = new Template();
        template.setName(name);
        CodeSpace codeSpace = this.codeSpaceRepository.findByPlaygroundIdAndDeletedFalse(playgroundId);
        template.setCodeZoneId(codeSpace.getCodeZoneId());
        template.setDeleted(false);
        template.setTemplateStatusEnum(TemplateStatusEnum.AVAILABLE);
        template.setTemplateTypeEnum(TemplateTypeEnum.BASE);
        template = this.templateRepository.save(template);
        return template;
    }

    public void importFile(MultipartFile file, Template template) {
        this.paasApiService.importFile(file,template.getCodeZoneId());
        CommitIdDTO commitIdDTO = this.gitService.addAndCommit(template.getId());
        template.setCommitId(commitIdDTO.getCommitId());
        template.setTemplateStatusEnum(TemplateStatusEnum.AVAILABLE);
        this.templateRepository.save(template);
    }

    public void delete(UserInfo userInfo, Long id) {
        Template template = this.get(id);
        template.setDeleted(true);
        this.templateRepository.save(template);
//        this.paasApiService.deleteCodeZone(userInfo,template.getCodeZoneId());
    }

    public List<Template> listUnitTest() {
        return this.templateRepository.findAllByDeletedFalseAndTemplateTypeEnum(TemplateTypeEnum.UNITTEST);
    }

    public List<Template> listMiddleware() {
        return this.templateRepository.findAllByDeletedFalseAndTemplateTypeEnum(TemplateTypeEnum.MIDDLEWARE);
    }
}
