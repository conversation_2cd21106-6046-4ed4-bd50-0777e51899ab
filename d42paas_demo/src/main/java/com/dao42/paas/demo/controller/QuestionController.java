package com.dao42.paas.demo.controller;

import com.dao42.paas.demo.controller.common.ApiV1Controller;
import com.dao42.paas.demo.convertor.QuestionConvertor;
import com.dao42.paas.demo.dto.ChooseQuestionDTO;
import com.dao42.paas.demo.dto.QuestionDTO;
import com.dao42.paas.demo.dto.ReleaseDTO;
import com.dao42.paas.demo.framework.currentUser.CurrentUser;
import com.dao42.paas.demo.framework.dto.result.ListResultDTO;
import com.dao42.paas.demo.framework.dto.result.ResultDTO;
import com.dao42.paas.demo.model.Question;
import com.dao42.paas.demo.model.UserInfo;
import com.dao42.paas.demo.service.QuestionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;


@RequiredArgsConstructor
@ApiV1Controller
@RequestMapping("/questions")
@Slf4j
@Api(tags = "问题")
@ApiSort()
public class QuestionController {

    @Autowired
    private QuestionService questionService;
    @Autowired
    private QuestionConvertor questionConvertor;

    @GetMapping(value = "/list")
    @ApiOperation(value = "list")
    @ApiOperationSupport(order = 1)
    public ListResultDTO<QuestionDTO> list(@CurrentUser @ApiIgnore UserInfo userInfo) {
        List<Question> questions = this.questionService.list();
        return this.questionConvertor.toResultDTO(questions);
    }

    @PutMapping(value = "/choose/{id}")
    @ApiOperation(value = "切题")
    @ApiOperationSupport(order = 2)
    public ResultDTO<Void> choose(@CurrentUser @ApiIgnore UserInfo userInfo,
                                  @RequestBody ChooseQuestionDTO chooseQuestionDTO) {
        this.questionService.choose(userInfo,chooseQuestionDTO);
        return ResultDTO.success();
    }

    @PostMapping
    @ApiOperation(value = "release")
    @ApiOperationSupport(order = 2)
    public ResultDTO<Void> create(@CurrentUser @ApiIgnore UserInfo userInfo,
                                  @RequestBody ReleaseDTO releaseDTO) {
        this.questionService.create(userInfo,releaseDTO);
        return ResultDTO.success();
    }
}
