package com.dao42.paas.demo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * TestCaseResultDTO
 */
@Getter
@Setter
public class TestCaseResultDTO {

    @ApiModelProperty("运行时间")
    private String time;

    @ApiModelProperty("结果")
    private String result;

    @ApiModelProperty("运行状态 运行中:STARTING,通过:PASS,不通过:FAIL,编译错误:COMPILE_ERROR,未知:UNKNOWN")
    private String runStatus;

    @ApiModelProperty("详细结果")
    private List<TestCaseResultItemDTO> testCaseResultItemDTOS = new ArrayList<>();

}
