package com.dao42.paas.demo.framework.utils;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 生成字符串的MD5摘要，生成的MD5字符串中的字母均为小写。
 */
public class MD5Util {
    public final static String md5(final String s) throws NoSuchAlgorithmException, UnsupportedEncodingException {
        final char hexDigits[] = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f' };
        final byte[] inputBytes = s.getBytes("UTF-8");
        final MessageDigest mdInst = MessageDigest.getInstance("md5");
        mdInst.update(inputBytes);
        final byte[] md = mdInst.digest();
        final int j = md.length;
        final char str[] = new char[j * 2];
        int k = 0;
        for (int i = 0; i < j; i++) {
            final byte byte0 = md[i];
            str[k++] = hexDigits[(byte0 >>> 4) & 0xf];
            str[k++] = hexDigits[byte0 & 0xf];
        }
        return new String(str);
    }
}
