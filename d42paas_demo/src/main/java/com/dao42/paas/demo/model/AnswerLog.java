package com.dao42.paas.demo.model;


import com.dao42.paas.demo.framework.jpa.model.AbstractAuditModel;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.ManyToOne;
import javax.validation.constraints.NotBlank;

/**
 * 答题记录
 */
@Getter
@Setter
@Entity
public class AnswerLog extends AbstractAuditModel {

    /**
     * playgroundId
     */
    private String playgroundId;

    /**
     * dockerID
     */
    @NotBlank
    private String dockerId;

    /**
     * 题目
     */
    @ManyToOne(cascade= CascadeType.ALL)
    private Question question;

    /**
     * 答题人
     */
    @ManyToOne(cascade= CascadeType.ALL)
    private UserInfo userInfo;

}
