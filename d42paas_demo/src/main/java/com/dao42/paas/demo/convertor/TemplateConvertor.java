package com.dao42.paas.demo.convertor;

import com.dao42.paas.demo.dto.TemplateDTO;
import com.dao42.paas.demo.framework.dto.AbstractConvertor;
import com.dao42.paas.demo.model.Template;
import org.springframework.stereotype.Component;

@Component
public class TemplateConvertor extends AbstractConvertor<Template, TemplateDTO> {

    @Override
    public Template toModel(TemplateDTO templateDTO) {
        throw new IllegalStateException();
    }

    @Override
    public TemplateDTO toDTO(Template template, boolean forListView) {
        final TemplateDTO templateDTO = new TemplateDTO();
        templateDTO.setId(template.getId());
        templateDTO.setName(template.getName());
        templateDTO.setPlaygroundId(template.getPlaygroundId());
        return templateDTO;
    }
}