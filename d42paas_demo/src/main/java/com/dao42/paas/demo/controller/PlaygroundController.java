package com.dao42.paas.demo.controller;

import com.dao42.paas.demo.controller.common.ApiV1Controller;
import com.dao42.paas.demo.dto.PlaygroundIdDTO;
import com.dao42.paas.demo.framework.currentUser.CurrentUser;
import com.dao42.paas.demo.framework.dto.result.ResultDTO;
import com.dao42.paas.demo.model.UserInfo;
import com.dao42.paas.demo.service.PlaygroundService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


@RequiredArgsConstructor
@ApiV1Controller
@RequestMapping("/playgrounds")
@Slf4j
@Api(tags = "playground")
@ApiSort()
public class PlaygroundController {

    @Autowired
    private PlaygroundService playgroundService;

    @PostMapping
    @ApiOperation(value = "create")
    @ApiOperationSupport(order = 1)
    public ResultDTO<PlaygroundIdDTO> create(@CurrentUser @ApiIgnore UserInfo userInfo,
                                             @RequestParam(required = false) @ApiIgnore String ideServerCode) {
        String playgroundId = this.playgroundService.create(userInfo,ideServerCode);
        PlaygroundIdDTO playgroundIdDTO = new PlaygroundIdDTO();
        playgroundIdDTO.setPlaygroundId(playgroundId);
        return ResultDTO.success(playgroundIdDTO);
    }

    @GetMapping("/{id}/files")
    @ApiOperation(value = "下载代码文件", notes = "不包含 node_modules 等依赖文件")
    public void download(
            @PathVariable final Long id, final HttpServletRequest request, final HttpServletResponse response) {
        this.playgroundService.download(id, request, response);
    }
}
