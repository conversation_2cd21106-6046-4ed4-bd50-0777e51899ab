package com.dao42.paas.demo.model;

import com.dao42.paas.demo.framework.jpa.model.AbstractAuditModel;
import javax.persistence.Entity;
import javax.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

/**
 * 题目
 */
@Getter
@Setter
@Entity
public class Question extends AbstractAuditModel {

    /**
     * 名称
     */
    @NotBlank
    private String name;

    /**
     * codeZoneSnapshotId
     */
    @NotBlank
    private String codeZoneSnapshotId;

    /**
     * 删除
     */
    private Boolean deleted;

    /**
     * 默认打开文件
     */
    private String defaultOpenFile;

}
