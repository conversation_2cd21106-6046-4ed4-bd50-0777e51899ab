server:
  # 服务端口
  port: 8001

spring:
  main:
    allow-bean-definition-overriding: true
  profiles:
    active: local
  # 服务名称
  application:
    name: dao42-paas-demo
  servlet:
    multipart:
      max-file-size: 20MB
      max-request-size: 100MB
  jackson:
    # json中日期是否用timestamps格式交互
    serialization.write-dates-as-timestamps: true
    # json中遇到无效变量是否报错
    deserialization.fail_on_unknown_properties: false
    # 时区
    time-zone: GMT+8
  # 数据库相关设置
  datasource:
    url: *****************************************************************************************************************************************************
    username: root
    password: rd123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    test-while-idle: true
    max-wait-millis: 30000
    validation-query: 'SELECT 1'
    time-between-eviction-runs-millis: 20000
    min-evictable-idle-time-millis: 28700
  # JPA相关设置
  jpa:
    database-platform: org.hibernate.dialect.MySQL5InnoDBDialect
    hibernate.ddl-auto: update
    show-sql: false
    open-in-view: true
    properties.hibernate.enable_lazy_load_no_trans: true