package com.dao42.paas.demo.controller;

import com.dao42.paas.demo.dto.UserInfoDTO;
import com.dao42.paas.demo.dto.UserInfoTokenDTO;
import com.dao42.paas.demo.framework.dto.result.ListResultDTO;
import com.dao42.paas.demo.framework.dto.result.ResultDTO;
import com.dao42.paas.demo.model.UserInfo;
import com.dao42.paas.demo.repository.UserInfoRepository;
import com.dao42.paas.demo.utils.CommonUtil;
import com.dao42.paas.demo.utils.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import org.junit.*;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpStatus;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import static com.dao42.paas.demo.utils.CommonUtil.userToken;
import static org.assertj.core.api.Assertions.assertThat;

/**
 * UserInfoControllerTest Tester.
 *
 * <AUTHOR>
 * @version 1.0
 * @since <pre>3月 9, 2022</pre>
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {com.dao42.paas.demo.Application.class})
@ActiveProfiles("test")
public class UserInfoControllerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;
    private MockMvc mvc;
    @Autowired
    private UserInfoRepository userInfoRepository;
    private UserInfo userInfo;

    @Before
    public void setup(){
        mvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        // 初始化数据
    }

    private void createUserInfo() {
        userInfo = new UserInfo();
        userInfo.setToken(userToken);
        userInfo.setName("test-user");
        userInfo.setDeleted(false);
        this.userInfoRepository.save(userInfo);
    }

    @After
    public void teardown(){
    }
    @BeforeClass
    public static void beforeClass() {
    }

    @AfterClass
    public static void afterClass() {
    }

    /**
     * login
     */
    @Test
    @Transactional
    public void testLogin() throws Exception {
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setName("user-name");
        String requestJson = JsonUtil.pojoToJson(userInfoDTO);
        MockHttpServletResponse response = CommonUtil.postMockPerform(mvc,"/demo/api/v1/users/login",requestJson);
        ResultDTO<UserInfoTokenDTO> resultDTO = JsonUtil.jsonToPojo(response.getContentAsString(),
                new TypeReference<ResultDTO<UserInfoTokenDTO>>(){});
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(resultDTO.getStatus().name()).isEqualTo("success");
        UserInfo userInfoNew = this.userInfoRepository.findByToken(resultDTO.getData().getToken());
        assertThat(userInfoNew).isNotNull();
    }

    /**
     * list
     */
    @Test
    @Transactional
    public void testList() throws Exception {
        this.createUserInfo();
        MockHttpServletResponse response = CommonUtil.getMockPerform(mvc,"/demo/api/v1/users",null);
        ListResultDTO<UserInfoDTO> resultDTO = JsonUtil.jsonToPojo(response.getContentAsString(),
                new TypeReference<ListResultDTO<UserInfoDTO>>(){});
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(resultDTO.getStatus().name()).isEqualTo("success");
        assertThat(resultDTO.getData().get(0).getId()).isEqualTo(userInfo.getId());
    }

}
