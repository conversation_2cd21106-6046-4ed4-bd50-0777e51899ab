package com.dao42.paas.demo.controller;

import com.dao42.paas.demo.dto.TicketDTO;
import com.dao42.paas.demo.framework.dto.result.ResultDTO;
import com.dao42.paas.demo.model.Template;
import com.dao42.paas.demo.model.UserInfo;
import com.dao42.paas.demo.repository.CodeSpaceRepository;
import com.dao42.paas.demo.repository.TemplateRepository;
import com.dao42.paas.demo.repository.UserInfoRepository;
import com.dao42.paas.demo.service.paas.PaasApiService;
import com.dao42.paas.demo.utils.CommonUtil;
import com.dao42.paas.demo.utils.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import org.junit.*;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import static com.dao42.paas.demo.utils.CommonUtil.userToken;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * TemplateControllerTest Tester.
 *
 * <AUTHOR>
 * @version 1.0
 * @since <pre>3月 9, 2022</pre>
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {com.dao42.paas.demo.Application.class})
@ActiveProfiles("test")
public class TicketControllerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;
    private MockMvc mvc;
    @Autowired
    private UserInfoRepository userInfoRepository;
    @Autowired
    private TemplateRepository templateRepository;
    @Autowired
    private CodeSpaceRepository codeSpaceRepository;
    @MockBean
    private PaasApiService paasApiService;
    private UserInfo userInfo;
    private Template template;

    @Before
    public void setup(){
        mvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        // 初始化数据
        this.createUserInfo();
        this.createTemplate();
    }

    private void createUserInfo() {
        userInfo = new UserInfo();
        userInfo.setToken(userToken);
        userInfo.setName("test-user");
        userInfo.setDeleted(false);
        this.userInfoRepository.save(userInfo);
    }

    private void createTemplate() {
        template = new Template();
        template.setCodeZoneId("test-codeZoneId");
        template.setCommitId("test-commitId");
        template.setDeleted(false);
        template.setName("test-name");
        template.setPlaygroundId("test-playgroundId");
        this.templateRepository.save(template);
    }

    @After
    public void teardown(){
    }
    @BeforeClass
    public static void beforeClass() {
    }

    @AfterClass
    public static void afterClass() {
    }

    /**
     * ticket
     */
    @Test
    @Transactional
    public void testTicket() throws Exception {
        when(paasApiService.getTicket(any(UserInfo.class),any(String.class),any(String.class))).thenReturn("test-ticket");
        MockHttpServletResponse response = CommonUtil.getMockPerform(mvc,"/demo/api/v1/tickets?playgroundId="+template.getPlaygroundId()+"&specialNameFromQueue=specialNameFromQueue",null);
        ResultDTO<TicketDTO> resultDTO = JsonUtil.jsonToPojo(response.getContentAsString(),
                new TypeReference<ResultDTO<TicketDTO>>(){});
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(resultDTO.getStatus().name()).isEqualTo("success");
        assertThat(resultDTO.getData().getTicket()).isEqualTo("test-ticket");
    }

}
