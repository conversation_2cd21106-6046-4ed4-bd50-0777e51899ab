package com.dao42.paas.demo.utils;

import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

/**
 * <AUTHOR>
 * @create 2022/3/13 4:40 下午
 */
public class CommonUtil {
    public static String userToken = "token";

    public static MockHttpServletResponse getMockPerform(MockMvc mvc, String apiPath, Long id) throws Exception {
        return mvc.perform(getRequestBuilder(apiPath,id))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andReturn().getResponse();
    }
    public static MockHttpServletResponse putMockPerform(MockMvc mvc, String apiPath, Long id,String requestJson) throws Exception {
        return mvc.perform(putRequestBuilder(apiPath,id,requestJson))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andReturn().getResponse();
    }
    public static MockHttpServletResponse postMockPerform(MockMvc mvc, String apiPath,String requestJson) throws Exception {
        return mvc.perform(postRequestBuilder(apiPath,requestJson))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andReturn().getResponse();
    }
    public static MockHttpServletResponse deleteMockPerform(MockMvc mvc, String apiPath,Long id) throws Exception {
        return mvc.perform(deleteRequestBuilder(apiPath,id))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andReturn().getResponse();
    }
    public static MockHttpServletResponse multipartMockPerform(MockMvc mvc, String apiPath,Long id, MockMultipartFile file) throws Exception {
        return mvc.perform(MockMvcRequestBuilders.multipart(apiPath,id)
                        .file(file)
                        .headers(getHeader())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andReturn().getResponse();
    }
    private static MockHttpServletRequestBuilder getRequestBuilder(String api, Long id){
        return MockMvcRequestBuilders.get(api,id)
                .headers(getHeader()).contentType(MediaType.APPLICATION_JSON);
    }
    private static MockHttpServletRequestBuilder postRequestBuilder(String api, String requestJson){
        return MockMvcRequestBuilders.post(api)
                .headers(getHeader()).content(requestJson).contentType(MediaType.APPLICATION_JSON);
    }
    private static MockHttpServletRequestBuilder putRequestBuilder(String api, Long id, String requestJson){
        return MockMvcRequestBuilders.put(api,id)
                .headers(getHeader()).content(requestJson).contentType(MediaType.APPLICATION_JSON);
    }
    private static MockHttpServletRequestBuilder deleteRequestBuilder(String api, Long id){
        return MockMvcRequestBuilders.delete(api,id)
                .headers(getHeader()).contentType(MediaType.APPLICATION_JSON);
    }

    public static HttpHeaders getHeader() {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("token",userToken);
        return httpHeaders;
    }
}
