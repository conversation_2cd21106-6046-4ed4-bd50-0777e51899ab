package com.dao42.paas.demo.controller;

import com.dao42.paas.demo.bean.ForkCodeZoneBean;
import com.dao42.paas.demo.dto.CodeSpaceDTO;
import com.dao42.paas.demo.dto.CommitIdDTO;
import com.dao42.paas.demo.dto.ForkCodeSpaceDTO;
import com.dao42.paas.demo.dto.PlaygroundIdDTO;
import com.dao42.paas.demo.framework.dto.result.ListResultDTO;
import com.dao42.paas.demo.framework.dto.result.ResultDTO;
import com.dao42.paas.demo.model.CodeSpace;
import com.dao42.paas.demo.model.Template;
import com.dao42.paas.demo.model.UserInfo;
import com.dao42.paas.demo.repository.CodeSpaceRepository;
import com.dao42.paas.demo.repository.TemplateRepository;
import com.dao42.paas.demo.repository.UserInfoRepository;
import com.dao42.paas.demo.service.paas.PaasApiService;
import com.dao42.paas.demo.utils.CommonUtil;
import com.dao42.paas.demo.utils.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import org.junit.*;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import java.util.HashMap;

import static com.dao42.paas.demo.utils.CommonUtil.userToken;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * CodeSpaceControllerTest Tester.
 *
 * <AUTHOR>
 * @version 1.0
 * @since <pre>3月 9, 2022</pre>
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {com.dao42.paas.demo.Application.class})
@ActiveProfiles("test")
public class CodeSpaceControllerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;
    private MockMvc mvc;
    @Autowired
    private UserInfoRepository userInfoRepository;
    @Autowired
    private TemplateRepository templateRepository;
    @Autowired
    private CodeSpaceRepository codeSpaceRepository;
    @MockBean
    private PaasApiService paasApiService;
    private UserInfo userInfo;
    private Template template;

    @Before
    public void setup(){
        mvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        // 初始化数据
        this.createUserInfo();
        this.createTemplate();
    }

    private void createUserInfo() {
        userInfo = new UserInfo();
        userInfo.setToken(userToken);
        userInfo.setName("test-user");
        userInfo.setDeleted(false);
        this.userInfoRepository.save(userInfo);
    }

    private void createTemplate() {
        template = new Template();
        template.setCodeZoneId("test-codeZoneId");
        template.setCommitId("test-commitId");
        template.setDeleted(false);
        template.setName("test-name");
        template.setPlaygroundId("test-playgroundId");
        this.templateRepository.save(template);
    }

    private CodeSpace createCodeSpace() {
        CodeSpace codeSpace = new CodeSpace();
        codeSpace.setDeleted(false);
        codeSpace.setPlaygroundId("test-playgroundId");
        codeSpace.setCodeZoneId("test-codeZoneId");
        codeSpace.setTemplate(template);
        codeSpace.setUserInfo(userInfo);
        codeSpace.setName("test-name");
        return this.codeSpaceRepository.save(codeSpace);
    }

    @After
    public void teardown(){
    }
    @BeforeClass
    public static void beforeClass() {
    }

    @AfterClass
    public static void afterClass() {
    }

    /**
     * fork
     */
    @Test
    @Transactional
    public void testFork() throws Exception {
        ForkCodeZoneBean forkCodeZoneBean = new ForkCodeZoneBean();
        forkCodeZoneBean.setId(1L);
        when(paasApiService.fork(any(HashMap.class),any(UserInfo.class))).thenReturn(forkCodeZoneBean);
        CommitIdDTO commitIdDTO = new CommitIdDTO();
        commitIdDTO.setCommitId("commitId");
        when(paasApiService.gitAddAndCommitId(any(String.class))).thenReturn(commitIdDTO);
        ForkCodeSpaceDTO forkCodeSpaceDTO = new ForkCodeSpaceDTO();
        forkCodeSpaceDTO.setName("fork-name");
        forkCodeSpaceDTO.setTemplateId(template.getId());
        String requestJson = JsonUtil.pojoToJson(forkCodeSpaceDTO);
        MockHttpServletResponse response = CommonUtil.postMockPerform(mvc,"/demo/api/v1/codes/fork",requestJson);
        ResultDTO<PlaygroundIdDTO> resultDTO = JsonUtil.jsonToPojo(response.getContentAsString(),
                new TypeReference<ResultDTO<PlaygroundIdDTO>>(){});
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(resultDTO.getStatus().name()).isEqualTo("success");
        CodeSpace codeSpace = this.codeSpaceRepository.findByPlaygroundIdAndDeletedFalse(resultDTO.getData().getPlaygroundId());
        assertThat(codeSpace).isNotNull();
    }
    /**
     * list
     */
    @Test
    @Transactional
    public void testList() throws Exception {
        CodeSpace codeSpace = this.createCodeSpace();
        MockHttpServletResponse response = CommonUtil.getMockPerform(mvc,"/demo/api/v1/codes",null);
        ListResultDTO<CodeSpaceDTO> resultDTO = JsonUtil.jsonToPojo(response.getContentAsString(),
                new TypeReference<ListResultDTO<CodeSpaceDTO>>(){});
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(resultDTO.getStatus().name()).isEqualTo("success");
        assertThat(resultDTO.getData().get(0).getId()).isEqualTo(codeSpace.getId());
    }

    /**
     * delete
     */
    @Test
    @Transactional
    public void testDelete() throws Exception {
        CodeSpace codeSpace = this.createCodeSpace();
        MockHttpServletResponse response = CommonUtil.deleteMockPerform(mvc,"/demo/api/v1/codes/{id}",codeSpace.getId());
        ListResultDTO<CodeSpaceDTO> resultDTO = JsonUtil.jsonToPojo(response.getContentAsString(),
                new TypeReference<ListResultDTO<CodeSpaceDTO>>(){});
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(resultDTO.getStatus().name()).isEqualTo("success");
        CodeSpace codeSpaceNew = this.codeSpaceRepository.findByIdAndDeletedFalse(codeSpace.getId());
        assertThat(codeSpaceNew).isNull();
    }

}
