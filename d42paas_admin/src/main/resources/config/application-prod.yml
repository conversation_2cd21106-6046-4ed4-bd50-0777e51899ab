knife4j:
  enable: true
  production: true

#配置数据源
spring:
  redis:
    #数据库索引
    database: ${REDIS_DB:1}
    host: ${REDIS_HOST:127.0.0.1}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PWD:}
    #连接超时时间
    timeout: 5000
  datasource:
    primary:
      driver-class-name: com.mysql.cj.jdbc.Driver
      jdbc-url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME:eladmin}?createDatabaseIfNotExist=TRUE&useUnicode=TRUE&characterEncoding=UTF-8&autoReconnect=TRUE&serverTimezone=GMT%2b8
      username: ${DB_USER:root}
      password: ${DB_PWD:123456}
    secondary:
      driver-class-name: com.mysql.cj.jdbc.Driver
      jdbc-url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME:paas}?createDatabaseIfNotExist=TRUE&useUnicode=TRUE&characterEncoding=UTF-8&autoReconnect=TRUE&serverTimezone=GMT%2b8
      username: ${DB_USER:root}
      password: ${DB_PWD:123456}

# 登录相关配置
login:
  # 登录缓存
  cache-enable: true
  #  是否限制单用户登录
  single-login: false
  #  验证码
  login-code:
    #  验证码类型配置 查看 LoginProperties 类
    code-type: arithmetic
    #  登录图形验证码有效时间/分钟
    expiration: 2
    #  验证码高度
    width: 111
    #  验证码宽度
    height: 36
    # 内容长度
    length: 2
    # 字体名称，为空则使用默认字体，如遇到线上乱码，设置其他字体即可
    font-name:
    # 字体大小
    font-size: 25

#jwt
jwt:
  header: Authorization
  # 令牌前缀
  token-start-with: Bearer
  # 必须使用最少88位的Base64对该令牌进行编码
  base64-secret: ZmQ0ZGI5NjQ0MDQwY2I4MjMxY2Y3ZmI3MjdhN2ZmMjNhODViOTg1ZGE0NTBjMGM4NDA5NzYxMjdjOWMwYWRmZTBlZjlhNGY3ZTg4Y2U3YTE1ODVkZDU5Y2Y3OGYwZWE1NzUzNWQ2YjFjZDc0NGMxZWU2MmQ3MjY1NzJmNTE0MzI=
  # 令牌过期时间 此处单位/毫秒 ，默认2小时，可在此网站生成 https://www.convertworld.com/zh-hans/time/milliseconds.html
  token-validity-in-seconds: 7200000
  # 在线用户key
  online-key: online-token-
  # 验证码
  code-key: code-key-
  # token 续期检查时间范围（默认30分钟，单位默认毫秒），在token即将过期的一段时间内用户操作了，则给用户的token续期
  detect: 1800000
  # 续期时间范围，默认 1小时，这里单位毫秒
  renew: 3600000

# IP 本地解析
ip:
  local-parsing: false

#是否允许生成代码，生产环境设置为false
generator:
  enabled: false

# 文件存储路径
file:
  mac:
    path: ~/file/
    avatar: ~/avatar/
  linux:
    path: /home/<USER>/file/
    avatar: /home/<USER>/avatar/
  windows:
    path: C:\eladmin\file\
    avatar: C:\eladmin\avatar\
  # 文件大小 /M
  maxSize: 100
  avatarMaxSize: 5
