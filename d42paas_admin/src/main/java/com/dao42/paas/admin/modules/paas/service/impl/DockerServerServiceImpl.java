package com.dao42.paas.admin.modules.paas.service.impl;

import com.dao42.paas.admin.modules.paas.service.DockerServerService;
import com.dao42.paas.admin.modules.paas.service.dto.DockerServerDto;
import com.dao42.paas.admin.modules.paas.service.mapstruct.DockerServerMapper;
import com.dao42.paas.admin.modules.paas.service.query.DockerServerQueryCriteria;
import com.dao42.paas.admin.utils.FileUtil;
import com.dao42.paas.admin.utils.PageUtil;
import com.dao42.paas.admin.utils.QueryHelp;
import com.dao42.paas.admin.utils.ValidationUtil;
import com.dao42.paas.common.enums.DockerServerStatus;
import com.dao42.paas.framework.dto.result.ResultDTO;
import com.dao42.paas.model.docker.DockerServer;
import com.dao42.paas.repository.docker.DockerServerRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 服务实现
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class DockerServerServiceImpl implements DockerServerService {

    private final DockerServerRepository dockerServerRepository;

    private final DockerServerMapper dockerServerMapper;

    private final RestTemplate restTemplate;

    private final ObjectMapper mapper;

    @Override
    public Map<String, Object> queryAll(DockerServerQueryCriteria criteria, Pageable pageable) {
        Page<DockerServer> page =
            dockerServerRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,
                criteria,
                criteriaBuilder), pageable);
        return PageUtil.toPage(page.map(dockerServerMapper::toDto));
    }

    @Override
    public List<DockerServerDto> queryAll(DockerServerQueryCriteria criteria) {
        return dockerServerMapper.toDto(dockerServerRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(
            root,
            criteria,
            criteriaBuilder)));
    }

    @Override
    @Transactional
    public DockerServerDto findById(Long id) {
        DockerServer dockerServer = dockerServerRepository.findById(id).orElseGet(DockerServer::new);
        ValidationUtil.isNull(dockerServer.getId(), "DockerServer", "id", id);
        return dockerServerMapper.toDto(dockerServer);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DockerServerDto create(DockerServer resources) {
        resources.setStatus(DockerServerStatus.INACTIVE);
        ResultDTO result = restTemplate.postForObject("/server", resources, ResultDTO.class);
        DockerServer data = mapper.convertValue(result.getData(), DockerServer.class);
        return dockerServerMapper.toDto(data);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(DockerServer resources) {
        DockerServer dockerServer = dockerServerRepository.findById(resources.getId()).orElseGet(DockerServer::new);
        ValidationUtil.isNull(dockerServer.getId(), "DockerServer", "id", resources.getId());
        dockerServer.copy(resources);
        dockerServerRepository.save(dockerServer);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            dockerServerRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<DockerServerDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (DockerServerDto dockerServer : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("通信证书路径", dockerServer.getDockerCaPath());
            map.put("内网IP", dockerServer.getIp());
            map.put("DockerServer状态", dockerServer.getStatus());
            map.put("公网IP", dockerServer.getPublicIp());
            map.put("cpu核数", dockerServer.getCpuCount());
            map.put("内存MB", dockerServer.getMemorymb());
            map.put(" 是否是临时扩展的Server", dockerServer.getTemp());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    /**
     * @param id id
     */
    @Override
    public void setOnline(Long id) {
        restTemplate.put("/server/" + id + "/online", null);
    }

    /**
     * @param id id
     */
    @Override
    public void setOffline(Long id) {
        restTemplate.put("/server/" + id + "/offline", null);
    }

}