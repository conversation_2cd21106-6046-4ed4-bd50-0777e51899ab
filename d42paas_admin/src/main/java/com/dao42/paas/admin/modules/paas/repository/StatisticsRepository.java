package com.dao42.paas.admin.modules.paas.repository;

import com.dao42.paas.admin.modules.paas.domain.Statistics;
import com.dao42.paas.admin.modules.paas.enums.StatisticsTypeEnum;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

public interface StatisticsRepository extends JpaRepository<Statistics, Long> {

    List<Statistics> findAllByDateIn(Collection<String> dates);

    Optional<Statistics> findByDateAndType(String date, StatisticsTypeEnum type);

}
