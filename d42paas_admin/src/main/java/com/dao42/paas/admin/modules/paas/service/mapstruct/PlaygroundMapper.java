package com.dao42.paas.admin.modules.paas.service.mapstruct;

import com.dao42.paas.admin.base.BaseMapper;
import com.dao42.paas.admin.modules.paas.service.dto.MiddlewareInstanceDto;
import com.dao42.paas.admin.modules.paas.service.dto.PlaygroundDto;
import com.dao42.paas.model.Playground;
import com.dao42.paas.model.tenant.Tenant;
import com.dao42.paas.repository.TenantRepository;
import org.mapstruct.DecoratedWith;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 **/
@Mapper(componentModel = "spring",
    uses = {DockerContainerMapper.class, IDEServerMapper.class},
    unmappedTargetPolicy = ReportingPolicy.IGNORE
)
@DecoratedWith(PlaygroundMapperDecorated.class)
public interface PlaygroundMapper extends BaseMapper<PlaygroundDto, Playground> {

}

abstract class PlaygroundMapperDecorated implements PlaygroundMapper {

    @Autowired
    @Qualifier("delegate")
    private PlaygroundMapper delegate;

    @Autowired
    private TenantMapper tenantMapper;

    @Autowired
    private TenantRepository tenantRepository;

    @Autowired
    private MiddlewareInstanceMapper middlewareInstanceMapper;

    @Override
    public PlaygroundDto toDto(Playground entity) {
        PlaygroundDto dto = delegate.toDto(entity);
        Optional<Tenant> byId = tenantRepository.findById(entity.getTenantId());
        byId.ifPresent(e -> dto.setTenant(tenantMapper.toDto(e)));
        if (entity.getDockerContainer() != null) {
            // 把中间件放到playground层级方便展示
            List<MiddlewareInstanceDto> dtoList =
                middlewareInstanceMapper.toDto(entity.getDockerContainer().getMiddlewares());
            dto.setMiddlewares(dtoList);
        }
        return dto;
    }

}