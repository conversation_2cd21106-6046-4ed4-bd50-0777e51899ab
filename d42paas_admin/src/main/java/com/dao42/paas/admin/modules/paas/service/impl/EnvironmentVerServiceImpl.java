package com.dao42.paas.admin.modules.paas.service.impl;

import com.dao42.paas.admin.exception.EntityExistException;
import com.dao42.paas.admin.modules.paas.service.EnvironmentVerService;
import com.dao42.paas.admin.modules.paas.service.dto.EnvironmentVerDto;
import com.dao42.paas.admin.modules.paas.service.mapstruct.EnvironmentVerMapper;
import com.dao42.paas.admin.modules.paas.service.query.EnvironmentVerQueryCriteria;
import com.dao42.paas.admin.utils.FileUtil;
import com.dao42.paas.admin.utils.PageUtil;
import com.dao42.paas.admin.utils.QueryHelp;
import com.dao42.paas.admin.utils.ValidationUtil;
import com.dao42.paas.model.EnvironmentVer;
import com.dao42.paas.repository.EnvironmentVerRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 服务实现
 **/
@Service
@RequiredArgsConstructor
public class EnvironmentVerServiceImpl implements EnvironmentVerService {

    private final EnvironmentVerRepository environmentVerRepository;
    private final EnvironmentVerMapper environmentVerMapper;

    @Override
    public Map<String, Object> queryAll(EnvironmentVerQueryCriteria criteria, Pageable pageable) {
        Page<EnvironmentVer> page = environmentVerRepository.findAll(
            (root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder),
            pageable);
        return PageUtil.toPage(page.map(environmentVerMapper::toDto));
    }

    @Override
    public List<EnvironmentVerDto> queryAll(EnvironmentVerQueryCriteria criteria) {
        return environmentVerMapper.toDto(environmentVerRepository.findAll(
            (root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
    }

    @Override
    @Transactional
    public EnvironmentVerDto findById(Long id) {
        EnvironmentVer environmentVer = environmentVerRepository.findById(id).orElseGet(EnvironmentVer::new);
        ValidationUtil.isNull(environmentVer.getId(), "EnvironmentVer", "id", id);
        return environmentVerMapper.toDto(environmentVer);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public EnvironmentVerDto create(EnvironmentVer resources) {

        if (environmentVerRepository.findByEnvVerKey(resources.getEnvVerKey()) != null) {
            throw new EntityExistException(EnvironmentVer.class, "env_ver_key", resources.getEnvVerKey());
        }
        return environmentVerMapper.toDto(environmentVerRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(EnvironmentVer resources) {
        EnvironmentVer environmentVer =
            environmentVerRepository.findById(resources.getId()).orElseGet(EnvironmentVer::new);
        ValidationUtil.isNull(environmentVer.getId(), "EnvironmentVer", "id", resources.getId());
        EnvironmentVer environmentVer1 = environmentVerRepository.findByEnvVerKey(resources.getEnvVerKey());
        if (environmentVer1 != null && !environmentVer1.getId().equals(environmentVer.getId())) {
            throw new EntityExistException(EnvironmentVer.class, "env_ver_key", resources.getEnvVerKey());
        }
        environmentVer.copy(resources);
        environmentVerRepository.save(environmentVer);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            environmentVerRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<EnvironmentVerDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (EnvironmentVerDto environmentVer : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put(" createdBy", environmentVer.getCreatedBy());
            map.put(" createdDate", environmentVer.getCreatedDate());
            map.put(" lastModifiedBy", environmentVer.getLastModifiedBy());
            map.put(" lastModifiedDate", environmentVer.getLastModifiedDate());
            map.put(" version", environmentVer.getVersion());
            map.put(" defaultVer", environmentVer.getDefaultVer());
            map.put(" frameworkVersion", environmentVer.getFrameworkVersion());
            map.put(" installNixCmd", environmentVer.getInstallNixCmd());
            map.put(" languageVersion", environmentVer.getLanguageVersion());
            map.put(" environmentId", environmentVer.getEnvironmentId());
            map.put(" name", environmentVer.getName());
            map.put(" envVerKey", environmentVer.getEnvVerKey());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}