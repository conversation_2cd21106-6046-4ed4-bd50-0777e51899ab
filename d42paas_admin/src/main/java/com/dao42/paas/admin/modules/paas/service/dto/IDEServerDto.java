package com.dao42.paas.admin.modules.paas.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @description /
 **/
@Data
public class IDEServerDto implements Serializable {

    /**
     * 防止精度丢失
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    private String createdBy;

    private Timestamp createdDate;

    private String lastModifiedBy;

    private Timestamp lastModifiedDate;

    private Long version;

    /**
     * IDE Server的code 会用做队列名
     */
    private String code;

    /**
     * 是否在线
     */
    private Boolean online;

    /**
     * 公网连接地址
     */
    private String publicurl;

    /**
     * 鉴权用的secret
     */
    private String secret;
}