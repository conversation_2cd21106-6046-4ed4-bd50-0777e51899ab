package com.dao42.paas.admin.modules.paas.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 **/
@Data
public class TenantDto implements Serializable {

    /**
     * 防止精度丢失
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    private String createdBy;

    private Timestamp createdDate;

    private String lastModifiedBy;

    private Timestamp lastModifiedDate;

    private Long version;

    /**
     * tenantCode
     */
    private String code;

    /**
     * 租户名
     */
    private String name;

    /**
     * tenantSecret
     */
    private String secret;

    /**
     * 代码目录下配置文件的文件名
     */
    private String configFileName;
}