package com.dao42.paas.admin.modules.paas.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum StatisticsTypeEnum {

    PLAYGROUND(1, "playground"),
    DOCKER_CONTAINER(2, "docker容器"),
    CODE_ZONE(2, "codezone"),
    TENANT_USER(2, "用户");

    private final Integer code;
    private final String description;

    public static StatisticsTypeEnum find(Integer code) {
        for (StatisticsTypeEnum value : StatisticsTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}