package com.dao42.paas.admin.config;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * PAAS公共代码扫描
 *
 * <AUTHOR>
 */
@ComponentScan(basePackages = {"com.dao42.paas.admin", "com.dao42.paas.framework.jpa"})
// 单数据源配置，需要扫描的包
//@EntityScan(basePackages = {"com.dao42.paas.admin.**.domain", "com.dao42.paas.model"})
//@EnableJpaRepositories(basePackages = {"com.dao42.paas.admin.**.repository", "com.dao42.paas.repository"})
@Configuration
public class PaasConfig {


}