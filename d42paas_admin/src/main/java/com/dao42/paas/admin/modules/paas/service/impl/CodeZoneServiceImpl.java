package com.dao42.paas.admin.modules.paas.service.impl;

import com.dao42.paas.admin.modules.paas.service.CodeZoneService;
import com.dao42.paas.admin.modules.paas.service.dto.CodeZoneDto;
import com.dao42.paas.admin.modules.paas.service.mapstruct.CodeZoneMapper;
import com.dao42.paas.admin.modules.paas.service.query.CodeZoneQueryCriteria;
import com.dao42.paas.admin.utils.FileUtil;
import com.dao42.paas.admin.utils.PageUtil;
import com.dao42.paas.admin.utils.QueryHelp;
import com.dao42.paas.admin.utils.ValidationUtil;
import com.dao42.paas.model.codezone.CodeZone;
import com.dao42.paas.repository.codezone.CodeZoneRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 服务实现
 */
@Service
@RequiredArgsConstructor
public class CodeZoneServiceImpl implements CodeZoneService {

    private final CodeZoneRepository codeZoneRepository;
    private final CodeZoneMapper codeZoneMapper;

    @Override
    public Map<String, Object> queryAll(CodeZoneQueryCriteria criteria, Pageable pageable) {
        Page<CodeZone> page =
            codeZoneRepository.findAll(
                (root, criteriaQuery, criteriaBuilder) ->
                    QueryHelp.getPredicate(root, criteria, criteriaBuilder),
                pageable);
        return PageUtil.toPage(page.map(codeZoneMapper::toDto));
    }

    @Override
    public List<CodeZoneDto> queryAll(CodeZoneQueryCriteria criteria) {
        return codeZoneMapper.toDto(
            codeZoneRepository.findAll(
                (root, criteriaQuery, criteriaBuilder) ->
                    QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
    }

    @Override
    @Transactional
    public CodeZoneDto findById(Long id) {
        CodeZone codeZone = codeZoneRepository.findById(id).orElseGet(CodeZone::new);
        ValidationUtil.isNull(codeZone.getId(), "CodeZone", "id", id);
        return codeZoneMapper.toDto(codeZone);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CodeZoneDto create(CodeZone resources) {

        return codeZoneMapper.toDto(codeZoneRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(CodeZone resources) {
        CodeZone codeZone = codeZoneRepository.findById(resources.getId()).orElseGet(CodeZone::new);
        ValidationUtil.isNull(codeZone.getId(), "CodeZone", "id", resources.getId());
        codeZone.copy(resources);
        codeZoneRepository.save(codeZone);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            codeZoneRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<CodeZoneDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (CodeZoneDto codeZone : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put(" createdBy", codeZone.getCreatedBy());
            map.put(" createdDate", codeZone.getCreatedDate());
            map.put(" lastModifiedBy", codeZone.getLastModifiedBy());
            map.put(" lastModifiedDate", codeZone.getLastModifiedDate());
            map.put(" version", codeZone.getVersion());
            map.put("磁盘路径", codeZone.getRootPath());
            map.put("启动命令", codeZone.getStartCmd());
            map.put("租户", codeZone.getTenantId());
            map.put(" userId", codeZone.getUserId());
            map.put("环境", codeZone.getEnvironmentVerId());
            map.put(" unitTestFrameworkId", codeZone.getUnitTestFrameworkId());
            map.put(" deleted", codeZone.getDeleted());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}
