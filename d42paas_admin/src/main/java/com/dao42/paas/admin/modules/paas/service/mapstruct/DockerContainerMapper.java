package com.dao42.paas.admin.modules.paas.service.mapstruct;

import com.dao42.paas.admin.base.BaseMapper;
import com.dao42.paas.admin.modules.paas.service.dto.DockerContainerDto;
import com.dao42.paas.common.constants.RedisPrefix;
import com.dao42.paas.model.Environment;
import com.dao42.paas.model.docker.DockerContainer;
import com.dao42.paas.utils.JsonUtil;
import com.github.dockerjava.api.model.CpuStatsConfig;
import com.github.dockerjava.api.model.CpuUsageConfig;
import com.github.dockerjava.api.model.MemoryStatsConfig;
import com.github.dockerjava.api.model.Statistics;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.DecoratedWith;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;

/**
 * <AUTHOR>
 **/
@Mapper(componentModel = "spring",
    uses = {DockerServerMapper.class, MiddlewareInstanceMapper.class},
    unmappedTargetPolicy = ReportingPolicy.IGNORE
)
@DecoratedWith(DockerContainerMapperDecorated.class)
public interface DockerContainerMapper extends BaseMapper<DockerContainerDto, DockerContainer> {

}

@Slf4j
abstract class DockerContainerMapperDecorated implements DockerContainerMapper {

    @Autowired
    @Qualifier("delegate")
    private DockerContainerMapper delegate;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public DockerContainerDto toDto(DockerContainer entity) {
        DockerContainerDto dto = delegate.toDto(entity);
        if (entity == null) {
            return null;
        }
        // 理论使用
        Environment environment = entity.getEnvironmentVer().getEnvironment();
        // fixme
//        dto.setMemoryLimit(environment.getResourcesLimit().getRAM());
//        dto.setTheoryMemoryUsage(environment.getResourcesLimit().getRAM());

        String json = stringRedisTemplate.opsForValue().get(RedisPrefix.PREFIX_DOCKER_STATS + entity.getId());
        // 实际上报
        Statistics statistics = JsonUtil.jsonToPojo(json, Statistics.class);

        //todo: 增加中间件
        if (statistics != null) {
            // 计算规则 https://docs.docker.com/engine/api/v1.41/#operation/ContainerStats
            CpuStatsConfig cpuStats = statistics.getCpuStats();
            CpuStatsConfig preCpuStats = statistics.getPreCpuStats();
            // cpu使用情况
            if(cpuStats != null && preCpuStats != null) {
                CpuUsageConfig cpuUsage = cpuStats.getCpuUsage();
                CpuUsageConfig preCpuUsage = preCpuStats.getCpuUsage();
                if(cpuUsage != null && preCpuUsage != null) {
                    long totalUsage = cpuUsage.getTotalUsage() == null ? 0 : cpuUsage.getTotalUsage();
                    long preTotalUsage = preCpuUsage.getTotalUsage() == null ? 0 : preCpuUsage.getTotalUsage();
                    long systemCpuUsage = cpuStats.getSystemCpuUsage() == null ? 0 : cpuStats.getSystemCpuUsage();
                    long preSystemCpuUsage =
                        preCpuStats.getSystemCpuUsage() == null ? 0 : preCpuStats.getSystemCpuUsage();
                    long onlineCpus = cpuStats.getOnlineCpus() == null ? 0 : cpuStats.getOnlineCpus();

                    // cpu_delta = cpu_stats.cpu_usage.total_usage - precpu_stats.cpu_usage.total_usage
                    long cpu_delta = totalUsage - preTotalUsage;
                    // system_cpu_delta = cpu_stats.system_cpu_usage - precpu_stats.system_cpu_usage
                    long system_cpu_delta = systemCpuUsage - preSystemCpuUsage;

                    // CPU usage % = (cpu_delta / system_cpu_delta) * number_cpus * 100.0
                    if(system_cpu_delta != 0) {
                        long cpuUsagePercent =
                            (long) ( ( (double) cpu_delta / system_cpu_delta ) * onlineCpus * 100.0 );
                        log.debug("CPU usage % {} = (long) ( ( (double) {} / {} ) * {} * 100 )",
                            cpuUsagePercent,
                            cpu_delta,
                            system_cpu_delta,
                            onlineCpus);
                        dto.setCpuUsedPercent(cpuUsagePercent);
                    }
                }
            }

            // 内存使用情况
            MemoryStatsConfig memoryStats = statistics.getMemoryStats();
            if(memoryStats != null && memoryStats.getStats() != null && memoryStats.getLimit() != null) {
                long usage = memoryStats.getUsage() == null ? 0 : memoryStats.getUsage();
                long cache = memoryStats.getStats().getCache() == null ? 0 : memoryStats.getStats().getCache();

                // used_memory = memory_stats.usage - memory_stats.stats.cache
                long used_memory = usage - cache;
                // available_memory = memory_stats.limit
                long available_memory = memoryStats.getLimit();

                // Memory usage % = (used_memory / available_memory) * 100.0
                if(available_memory != 0) {
                    long memoryUsagePercent = (long) ( ( (double) used_memory / available_memory ) * 100 );
                    log.debug("Memory usage % {} = (long) ( ( (double) {} / {} ) * 100 )",
                        memoryUsagePercent,
                        used_memory,
                        available_memory);
                    dto.setMemUsedPercent(memoryUsagePercent);
                }
                dto.setActualMemoryUsage(used_memory / 1024 / 1024);
            }
        }
        return dto;
    }

}