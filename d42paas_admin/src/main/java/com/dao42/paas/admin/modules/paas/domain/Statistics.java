package com.dao42.paas.admin.modules.paas.domain;

import com.dao42.paas.admin.base.BaseEntity;
import com.dao42.paas.admin.modules.paas.enums.StatisticsTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.*;
import javax.validation.constraints.NotNull;

/**
 * 统计信息
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@Entity
@Getter
@Setter
@Accessors(chain = true)
@Table(name = "paas_statistics", uniqueConstraints = {@UniqueConstraint(columnNames = {"date", "type"})})
public class Statistics extends BaseEntity {

    public Statistics(String date, StatisticsTypeEnum type) {
        this.date = date;
        this.type = type;
    }

    public Statistics(String date, StatisticsTypeEnum type, Long grow, Long total) {
        this.date = date;
        this.type = type;
        this.grow = grow;
        this.total = total;
    }

    @Id
    @Column(name = "id")
    @NotNull(groups = Update.class)
    @ApiModelProperty(value = "ID", hidden = true)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 日期
     */
    @Column(name = "date")
    private String date;

    /**
     * 数据类别
     */
    @Column(name = "type")
    private StatisticsTypeEnum type;

    /**
     * 增长
     */
    @Column(name = "grow")
    private long grow;

    /**
     * 总量
     */
    @Column(name = "total")
    private long total;

}
