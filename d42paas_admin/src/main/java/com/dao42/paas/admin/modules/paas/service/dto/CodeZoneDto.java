package com.dao42.paas.admin.modules.paas.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @description /
 */
@Data
public class CodeZoneDto implements Serializable {

    /**
     * 防止精度丢失
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    private String createdBy;

    private Timestamp createdDate;

    private String lastModifiedBy;

    private Timestamp lastModifiedDate;

    private Long version;

    /**
     * 磁盘路径
     */
    private String rootPath;

    /**
     * 启动命令
     */
    private String startCmd;

    /**
     * 租户
     */
    private Long tenantId;

    private String userId;

    /**
     * 环境
     */
    private Long environmentVerId;

    private Long unitTestFrameworkId;

    private Boolean deleted;
}
