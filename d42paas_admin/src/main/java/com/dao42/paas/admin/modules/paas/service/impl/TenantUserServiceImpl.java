package com.dao42.paas.admin.modules.paas.service.impl;

import com.dao42.paas.admin.modules.paas.service.TenantUserService;
import com.dao42.paas.admin.modules.paas.service.dto.TenantUserDto;
import com.dao42.paas.admin.modules.paas.service.mapstruct.TenantUserMapper;
import com.dao42.paas.admin.modules.paas.service.query.TenantUserQueryCriteria;
import com.dao42.paas.admin.utils.FileUtil;
import com.dao42.paas.admin.utils.PageUtil;
import com.dao42.paas.admin.utils.QueryHelp;
import com.dao42.paas.admin.utils.ValidationUtil;
import com.dao42.paas.model.user.TenantUser;
import com.dao42.paas.repository.user.TenantUserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 服务实现
 **/
@Service
@RequiredArgsConstructor
public class TenantUserServiceImpl implements TenantUserService {

    private final TenantUserRepository tenantUserRepository;
    private final TenantUserMapper tenantUserMapper;

    @Override
    public Map<String, Object> queryAll(TenantUserQueryCriteria criteria, Pageable pageable) {
        Page<TenantUser> page = tenantUserRepository.findAll(
            (root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder),
            pageable);
        return PageUtil.toPage(page.map(tenantUserMapper::toDto));
    }

    @Override
    public List<TenantUserDto> queryAll(TenantUserQueryCriteria criteria) {
        return tenantUserMapper.toDto(tenantUserRepository.findAll(
            (root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
    }

    @Override
    @Transactional
    public TenantUserDto findById(Long id) {
        TenantUser tenantUser = tenantUserRepository.findById(id).orElseGet(TenantUser::new);
        ValidationUtil.isNull(tenantUser.getId(), "TenantUser", "id", id);
        return tenantUserMapper.toDto(tenantUser);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TenantUserDto create(TenantUser resources) {

        return tenantUserMapper.toDto(tenantUserRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(TenantUser resources) {
        TenantUser tenantUser = tenantUserRepository.findById(resources.getId()).orElseGet(TenantUser::new);
        ValidationUtil.isNull(tenantUser.getId(), "TenantUser", "id", resources.getId());
        tenantUser.copy(resources);
        tenantUserRepository.save(tenantUser);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            tenantUserRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<TenantUserDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (TenantUserDto tenantUser : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put(" createdBy", tenantUser.getCreatedBy());
            map.put(" createdDate", tenantUser.getCreatedDate());
            map.put(" lastModifiedBy", tenantUser.getLastModifiedBy());
            map.put(" lastModifiedDate", tenantUser.getLastModifiedDate());
            map.put(" version", tenantUser.getVersion());
            map.put(" email", tenantUser.getEmail());
            map.put(" name", tenantUser.getName());
            map.put(" phoneNumber", tenantUser.getPhoneNumber());
            map.put(" userId", tenantUser.getUserId());
            map.put(" tenantId", tenantUser.getTenantId());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}