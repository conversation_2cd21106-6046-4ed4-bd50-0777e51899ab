package com.dao42.paas.admin.modules.paas.service.impl;

import com.dao42.paas.admin.exception.EntityExistException;
import com.dao42.paas.admin.modules.paas.service.IDEServerService;
import com.dao42.paas.admin.modules.paas.service.dto.IDEServerDto;
import com.dao42.paas.admin.modules.paas.service.mapstruct.IDEServerMapper;
import com.dao42.paas.admin.modules.paas.service.query.IDEServerQueryCriteria;
import com.dao42.paas.admin.utils.FileUtil;
import com.dao42.paas.admin.utils.PageUtil;
import com.dao42.paas.admin.utils.QueryHelp;
import com.dao42.paas.admin.utils.ValidationUtil;
import com.dao42.paas.model.IDEServer;
import com.dao42.paas.repository.IDEServerRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 服务实现
 **/
@Service
@RequiredArgsConstructor
public class IDEServerServiceImpl implements IDEServerService {

    private final IDEServerRepository ideServerRepository;
    private final IDEServerMapper ideServerMapper;

    @Override
    public Map<String, Object> queryAll(IDEServerQueryCriteria criteria, Pageable pageable) {
        Page<IDEServer> page = ideServerRepository.findAll(
            (root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder),
            pageable);
        return PageUtil.toPage(page.map(ideServerMapper::toDto));
    }

    @Override
    public List<IDEServerDto> queryAll(IDEServerQueryCriteria criteria) {
        return ideServerMapper.toDto(ideServerRepository.findAll(
            (root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
    }

    @Override
    @Transactional
    public IDEServerDto findById(Long id) {
        IDEServer ideServer = ideServerRepository.findById(id).orElseGet(IDEServer::new);
        ValidationUtil.isNull(ideServer.getId(), "IDEServer", "id", id);
        return ideServerMapper.toDto(ideServer);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public IDEServerDto create(IDEServer resources) {

        if (ideServerRepository.findByCode(resources.getCode()) != null) {
            throw new EntityExistException(IDEServer.class, "code", resources.getCode());
        }
        return ideServerMapper.toDto(ideServerRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(IDEServer resources) {
        IDEServer ideServer = ideServerRepository.findById(resources.getId()).orElseGet(IDEServer::new);
        ValidationUtil.isNull(ideServer.getId(), "IDEServer", "id", resources.getId());
        IDEServer ideServer1 = ideServerRepository.findByCode(resources.getCode());
        if (ideServer1 != null && !ideServer.getId().equals(ideServer1.getId())) {
            throw new EntityExistException(IDEServer.class, "code", resources.getCode());
        }
        ideServer.copy(resources);
        ideServerRepository.save(ideServer);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            ideServerRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<IDEServerDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (IDEServerDto ideServer : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put(" createdBy", ideServer.getCreatedBy());
            map.put(" createdDate", ideServer.getCreatedDate());
            map.put(" lastModifiedBy", ideServer.getLastModifiedBy());
            map.put(" lastModifiedDate", ideServer.getLastModifiedDate());
            map.put(" version", ideServer.getVersion());
            map.put("IDE Server的code 会用做队列名", ideServer.getCode());
            map.put("是否在线", ideServer.getOnline());
            map.put("公网连接地址", ideServer.getPublicurl());
            map.put("鉴权用的secret", ideServer.getSecret());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}