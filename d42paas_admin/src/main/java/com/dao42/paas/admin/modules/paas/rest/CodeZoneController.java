package com.dao42.paas.admin.modules.paas.rest;

import com.dao42.paas.admin.annotation.Log;
import com.dao42.paas.admin.modules.paas.service.CodeZoneService;
import com.dao42.paas.admin.modules.paas.service.query.CodeZoneQueryCriteria;
import com.dao42.paas.model.codezone.CodeZone;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "codeZone管理")
@RequestMapping("/api/codeZone")
public class CodeZoneController {

    private final CodeZoneService codeZoneService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('codeZone:list')")
    public void exportCodeZone(HttpServletResponse response, CodeZoneQueryCriteria criteria)
        throws IOException {
        codeZoneService.download(codeZoneService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询codeZone")
    @ApiOperation("查询codeZone")
    @PreAuthorize("@el.check('codeZone:list')")
    public ResponseEntity<Object> queryCodeZone(CodeZoneQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(codeZoneService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增codeZone")
    @ApiOperation("新增codeZone")
    @PreAuthorize("@el.check('codeZone:add')")
    public ResponseEntity<Object> createCodeZone(@Validated @RequestBody CodeZone resources) {
        return new ResponseEntity<>(codeZoneService.create(resources), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改codeZone")
    @ApiOperation("修改codeZone")
    @PreAuthorize("@el.check('codeZone:edit')")
    public ResponseEntity<Object> updateCodeZone(@Validated @RequestBody CodeZone resources) {
        codeZoneService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除codeZone")
    @ApiOperation("删除codeZone")
    @PreAuthorize("@el.check('codeZone:del')")
    public ResponseEntity<Object> deleteCodeZone(@RequestBody Long[] ids) {
        codeZoneService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
