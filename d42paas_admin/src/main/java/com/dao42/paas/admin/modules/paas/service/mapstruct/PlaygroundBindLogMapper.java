package com.dao42.paas.admin.modules.paas.service.mapstruct;

import com.dao42.paas.admin.base.BaseMapper;
import com.dao42.paas.admin.modules.paas.service.dto.PlaygroundBindLogDto;
import com.dao42.paas.model.PlaygroundBindLog;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 **/
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface PlaygroundBindLogMapper extends BaseMapper<PlaygroundBindLogDto, PlaygroundBindLog> {

}