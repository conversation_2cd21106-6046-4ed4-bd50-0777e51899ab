package com.dao42.paas.admin.modules.paas.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description /
 **/
@Data
public class DockerServerDto implements Serializable {

    /**
     * 防止精度丢失
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    /**
     * 通信证书路径
     */
    private String dockerCaPath;

    /**
     * 内网IP
     */
    private String ip;

    /**
     * DockerServer状态
     */
    private String status;

    /**
     * 公网IP
     */
    private String publicIp;

    /**
     * cpu核数
     */
    private Integer cpuCount;

    /**
     * 内存MB
     */
    private Integer memorymb;

    /**
     * 是否是临时扩展的Server
     */
    private Boolean temp;
}