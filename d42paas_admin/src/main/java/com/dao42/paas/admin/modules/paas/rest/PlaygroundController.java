package com.dao42.paas.admin.modules.paas.rest;

import com.dao42.paas.admin.annotation.Log;
import com.dao42.paas.admin.modules.paas.service.PlaygroundBindLogService;
import com.dao42.paas.admin.modules.paas.service.PlaygroundService;
import com.dao42.paas.admin.modules.paas.service.query.PlaygroundBindLogQueryCriteria;
import com.dao42.paas.admin.modules.paas.service.query.PlaygroundQueryCriteria;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 **/
@RestController
@RequiredArgsConstructor
@Api(tags = "playground管理")
@RequestMapping("/api/playground")
public class PlaygroundController {

    private final PlaygroundService playgroundService;
    private final PlaygroundBindLogService playgroundBindLogService;

    @GetMapping
    @Log("查询playground")
    @ApiOperation("查询playground")
    @PreAuthorize("@el.check('playground:list')")
    public ResponseEntity<Object> queryPlayground(PlaygroundQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(playgroundService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @GetMapping("/bindLog")
    @Log("查询bindLog")
    @ApiOperation("查询bindLog")
    @PreAuthorize("@el.check('playground:bindLog:list')")
    public ResponseEntity<Object> queryPlaygroundBindLog(PlaygroundBindLogQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(playgroundBindLogService.queryAll(criteria, pageable), HttpStatus.OK);
    }

}