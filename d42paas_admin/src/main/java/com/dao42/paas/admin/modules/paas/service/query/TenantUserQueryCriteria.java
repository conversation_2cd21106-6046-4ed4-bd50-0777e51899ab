package com.dao42.paas.admin.modules.paas.service.query;

import com.dao42.paas.admin.annotation.Query;
import lombok.Data;

/**
 * <AUTHOR>
 **/
@Data
public class TenantUserQueryCriteria {

    /**
     * 模糊
     */
    @Query(type = Query.Type.INNER_LIKE)
    private String email;

    /**
     * 模糊
     */
    @Query(type = Query.Type.INNER_LIKE)
    private String name;

    /**
     * 模糊
     */
    @Query(type = Query.Type.INNER_LIKE)
    private String phoneNumber;
}