package com.dao42.paas.admin.modules.paas.service;

import com.dao42.paas.admin.modules.paas.domain.Statistics;
import com.dao42.paas.admin.modules.paas.enums.StatisticsTypeEnum;
import com.dao42.paas.admin.modules.paas.repository.StatisticsRepository;
import com.dao42.paas.admin.utils.DateUtil;
import com.dao42.paas.repository.PlaygroundRepository;
import com.dao42.paas.repository.codezone.CodeZoneRepository;
import com.dao42.paas.repository.docker.DockerRepository;
import com.dao42.paas.repository.user.TenantUserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.stream.CollectorUtil.groupingBy;

@Slf4j
@CacheConfig(cacheNames = "statistics")
@RequiredArgsConstructor
@Service
public class StatisticsService {

    private final StatisticsRepository statisticsRepository;

    private final PlaygroundRepository playgroundRepository;

    private final DockerRepository dockerRepository;

    private final CodeZoneRepository codeZoneRepository;

    private final TenantUserRepository tenantUserRepository;

    @Cacheable
    public Map<String, Object> getPreviousDays(Integer days) {
        LocalDate today = LocalDate.now();
        List<String> queryDates = new ArrayList<>(days);
        for (int i = 1; i <= days; i++) {
            queryDates.add(today.minusDays(i).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        }
        List<Statistics> data = statisticsRepository.findAllByDateIn(queryDates);

        // 查询出的数据可能缺少某天，或者某个维度
        fillZero(queryDates, data);

        // 按照统计维度分组
        Map<StatisticsTypeEnum, List<Statistics>> collect = data.stream().collect(groupingBy(Statistics::getType));

        // 图表数据
        Map<StatisticsTypeEnum, Map<String, List<Long>>> chartData = new HashMap<>(StatisticsTypeEnum.values().length);
        for (Map.Entry<StatisticsTypeEnum, List<Statistics>> entry : collect.entrySet()) {
            List<Statistics> list = entry.getValue().stream().sorted(Comparator.comparing(Statistics::getDate))
                .collect(Collectors.toList());
            HashMap<String, List<Long>> c = new HashMap<>(2);
            c.put("grow", list.stream().map(Statistics::getGrow).collect(Collectors.toList()));
            c.put("total", list.stream().map(Statistics::getTotal).collect(Collectors.toList()));
            chartData.put(entry.getKey(), c);
        }

        // 最终结果
        Map<String, Object> result = new HashMap<>(2);
        result.put("date", queryDates.stream().sorted().collect(Collectors.toList()));
        result.put("chartData", chartData);
        return result;
    }

    /**
     * 定时任务调用，每日生成昨天的统计数据
     */
    @CacheEvict(allEntries = true)
    public void generateYesterday() {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        this.generate(yesterday);
    }

    /**
     * 生成指定天数的统计数据
     *
     * @param days 天数（从昨天算起）
     */
    @CacheEvict(allEntries = true)
    public void generateRecentDays(Integer days) {
        LocalDate today = LocalDate.now();
        for (int i = 1; i <= days; i++) {
            this.generate(today.minusDays(i));
        }
    }

    /**
     * 生成统计数据
     *
     * @param date 统计日期
     * @return 统计数据
     */
    private List<Statistics> generate(LocalDate date) {
        Date start = DateUtil.toDate(LocalDateTime.of(date, LocalTime.MIN));
        Date end = DateUtil.toDate(LocalDateTime.of(date, LocalTime.MAX));
        // 新增
        long a = playgroundRepository.countByCreatedDateBetween(start, end);
        long b = dockerRepository.countByCreatedDateBetween(start, end);
        long c = codeZoneRepository.countByCreatedDateBetween(start, end);
        long d = tenantUserRepository.countByCreatedDateBetween(start, end);

        long a2 = playgroundRepository.countByCreatedDateBefore(end);
        long b2 = dockerRepository.countByCreatedDateBefore(end);
        long c2 = codeZoneRepository.countByCreatedDateBefore(end);
        long d2 = tenantUserRepository.countByCreatedDateBefore(end);

        String dateStr = date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        List<Statistics> list = new ArrayList<>(4);

        // 插入或更新
        Statistics playground = statisticsRepository.findByDateAndType(dateStr, StatisticsTypeEnum.PLAYGROUND)
            .orElse(new Statistics(dateStr, StatisticsTypeEnum.PLAYGROUND));
        list.add(playground.setGrow(a).setTotal(a2));

        Statistics docker = statisticsRepository.findByDateAndType(dateStr, StatisticsTypeEnum.DOCKER_CONTAINER)
            .orElse(new Statistics(dateStr, StatisticsTypeEnum.DOCKER_CONTAINER));
        list.add(docker.setGrow(b).setTotal(b2));

        Statistics codeZone = statisticsRepository.findByDateAndType(dateStr, StatisticsTypeEnum.CODE_ZONE)
            .orElse(new Statistics(dateStr, StatisticsTypeEnum.CODE_ZONE));
        list.add(codeZone.setGrow(c).setTotal(c2));

        Statistics user = statisticsRepository.findByDateAndType(dateStr, StatisticsTypeEnum.TENANT_USER)
            .orElse(new Statistics(dateStr, StatisticsTypeEnum.TENANT_USER));
        list.add(user.setGrow(d).setTotal(d2));

        statisticsRepository.saveAll(list);
        return list;
    }

    /**
     * 为没有数据的日期或类型添加为0的假数据
     *
     * @param queryDates 预期查询日期
     * @param data       查询结果集合
     */
    private void fillZero(List<String> queryDates, List<Statistics> data) {
        queryDates.forEach(date -> {
            for (StatisticsTypeEnum type : StatisticsTypeEnum.values()) {
                Optional<Statistics> any =
                    data.stream().filter(s -> type.equals(s.getType()) && s.getDate().equals(date)).findAny();
                if (!any.isPresent()) {
                    data.add(new Statistics(date, type, 0L, 0L));
                }
            }
        });
    }

}
