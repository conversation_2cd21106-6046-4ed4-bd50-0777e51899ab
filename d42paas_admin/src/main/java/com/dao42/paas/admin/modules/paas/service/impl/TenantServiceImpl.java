package com.dao42.paas.admin.modules.paas.service.impl;

import com.dao42.paas.admin.exception.EntityExistException;
import com.dao42.paas.admin.modules.paas.service.TenantService;
import com.dao42.paas.admin.modules.paas.service.dto.TenantDto;
import com.dao42.paas.admin.modules.paas.service.mapstruct.TenantMapper;
import com.dao42.paas.admin.modules.paas.service.query.TenantQueryCriteria;
import com.dao42.paas.admin.utils.FileUtil;
import com.dao42.paas.admin.utils.PageUtil;
import com.dao42.paas.admin.utils.QueryHelp;
import com.dao42.paas.admin.utils.ValidationUtil;
import com.dao42.paas.model.tenant.Tenant;
import com.dao42.paas.repository.TenantRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 服务实现
 **/
@Service
@RequiredArgsConstructor
public class TenantServiceImpl implements TenantService {

    private final TenantRepository tenantRepository;
    private final TenantMapper tenantMapper;

    @Override
    public Map<String, Object> queryAll(TenantQueryCriteria criteria, Pageable pageable) {
        Page<Tenant> page = tenantRepository.findAll(
            (root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder),
            pageable);
        return PageUtil.toPage(page.map(tenantMapper::toDto));
    }

    @Override
    public List<TenantDto> queryAll(TenantQueryCriteria criteria) {
        return tenantMapper.toDto(tenantRepository.findAll(
            (root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
    }

    @Override
    @Transactional
    public TenantDto findById(Long id) {
        Tenant tenant = tenantRepository.findById(id).orElseGet(Tenant::new);
        ValidationUtil.isNull(tenant.getId(), "Tenant", "id", id);
        return tenantMapper.toDto(tenant);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TenantDto create(Tenant resources) {
        if (tenantRepository.findByCode(resources.getCode()) != null) {
            throw new EntityExistException(Tenant.class, "code", resources.getCode());
        }
        if (tenantRepository.findByName(resources.getName()) != null) {
            throw new EntityExistException(Tenant.class, "name", resources.getName());
        }
        if (tenantRepository.findBySecret(resources.getSecret()) != null) {
            throw new EntityExistException(Tenant.class, "secret", resources.getSecret());
        }
        return tenantMapper.toDto(tenantRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Tenant resources) {
        Tenant tenant = tenantRepository.findById(resources.getId()).orElseGet(Tenant::new);
        ValidationUtil.isNull(tenant.getId(), "Tenant", "id", resources.getId());
        Tenant tenant1 = tenantRepository.findByCode(resources.getCode());
        if (tenant1 != null && !tenant1.getId().equals(tenant.getId())) {
            throw new EntityExistException(Tenant.class, "code", resources.getCode());
        }
        tenant1 = tenantRepository.findByName(resources.getName());
        if (tenant1 != null && !tenant1.getId().equals(tenant.getId())) {
            throw new EntityExistException(Tenant.class, "name", resources.getName());
        }
        tenant1 = tenantRepository.findBySecret(resources.getSecret());
        if (tenant1 != null && !tenant1.getId().equals(tenant.getId())) {
            throw new EntityExistException(Tenant.class, "secret", resources.getSecret());
        }
        tenant.copy(resources);
        tenantRepository.save(tenant);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            tenantRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<TenantDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (TenantDto tenant : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put(" createdBy", tenant.getCreatedBy());
            map.put(" createdDate", tenant.getCreatedDate());
            map.put(" lastModifiedBy", tenant.getLastModifiedBy());
            map.put(" lastModifiedDate", tenant.getLastModifiedDate());
            map.put(" version", tenant.getVersion());
            map.put(" code", tenant.getCode());
            map.put(" name", tenant.getName());
            map.put(" secret", tenant.getSecret());
            map.put(" configFileName", tenant.getConfigFileName());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}