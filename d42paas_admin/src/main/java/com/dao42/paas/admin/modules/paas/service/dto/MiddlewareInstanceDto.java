package com.dao42.paas.admin.modules.paas.service.dto;

import com.dao42.paas.enums.DockerStatus;
import lombok.Data;

import java.io.Serializable;

@Data
public class MiddlewareInstanceDto implements Serializable {

    private Long id;

    /**
     * 资源容器的状态
     */
    private DockerStatus status;

    /**
     * 资源在宿主机上绑定的端口
     */
    private int hostBindPort;

    /**
     * middleware所在容器的ID
     * 未启动时为空
     */
    private String containerId;

    /**
     * 实际CPU使用率
     */
    private Long cpuUsedPercent;

    /**
     * 实际内存使用率
     */
    private Long memUsedPercent;

    /**
     * 实际内存使用值（MB）
     */
    private Long actualMemoryUsage;

    /**
     * 理论内存使用
     */
    private Integer theoryMemoryUsage;

    /**
     * 内存上限（从dockerContainer->envVer->env的 memoryMB 取值）
     */
    private Integer memoryLimit;

}
