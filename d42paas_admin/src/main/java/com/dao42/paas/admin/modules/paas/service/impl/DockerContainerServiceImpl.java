package com.dao42.paas.admin.modules.paas.service.impl;

import com.dao42.paas.admin.modules.paas.service.DockerContainerService;
import com.dao42.paas.admin.modules.paas.service.dto.DockerContainerDto;
import com.dao42.paas.admin.modules.paas.service.mapstruct.DockerContainerMapper;
import com.dao42.paas.admin.modules.paas.service.query.DockerContainerQueryCriteria;
import com.dao42.paas.admin.utils.FileUtil;
import com.dao42.paas.admin.utils.PageUtil;
import com.dao42.paas.admin.utils.QueryHelp;
import com.dao42.paas.admin.utils.ValidationUtil;
import com.dao42.paas.model.docker.DockerContainer;
import com.dao42.paas.repository.docker.DockerRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 服务实现
 **/
@Service
@RequiredArgsConstructor
public class DockerContainerServiceImpl implements DockerContainerService {

    private final DockerRepository DockerRepository;

    private final DockerContainerMapper dockerContainerMapper;

    @Override
    public Map<String, Object> queryAll(DockerContainerQueryCriteria criteria, Pageable pageable) {
        Page<DockerContainer> page =
            DockerRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,
                criteria,
                criteriaBuilder), pageable);
        return PageUtil.toPage(page.map(dockerContainerMapper::toDto));
    }

    @Override
    public List<DockerContainerDto> queryAll(DockerContainerQueryCriteria criteria) {
        return dockerContainerMapper.toDto(DockerRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(
            root,
            criteria,
            criteriaBuilder)));
    }

    @Override
    @Transactional
    public DockerContainerDto findById(Long id) {
        DockerContainer dockerContainer = DockerRepository.findById(id).orElseGet(DockerContainer::new);
        ValidationUtil.isNull(dockerContainer.getId(), "DockerContainer", "id", id);
        return dockerContainerMapper.toDto(dockerContainer);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DockerContainerDto create(DockerContainer resources) {

        return dockerContainerMapper.toDto(DockerRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(DockerContainer resources) {
        DockerContainer dockerContainer = DockerRepository.findById(resources.getId()).orElseGet(DockerContainer::new);
        ValidationUtil.isNull(dockerContainer.getId(), "DockerContainer", "id", resources.getId());
        dockerContainer.copy(resources);
        DockerRepository.save(dockerContainer);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            DockerRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<DockerContainerDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (DockerContainerDto dockerContainer : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put(" createdBy", dockerContainer.getCreatedBy());
            map.put(" createdDate", dockerContainer.getCreatedDate());
            map.put(" lastModifiedBy", dockerContainer.getLastModifiedBy());
            map.put(" lastModifiedDate", dockerContainer.getLastModifiedDate());
            map.put(" version", dockerContainer.getVersion());
            map.put(" containerId", dockerContainer.getContainerId());
            map.put(" deleted", dockerContainer.getDeleted());
            map.put(" hostPort", dockerContainer.getHostPort());
            map.put(" image", dockerContainer.getImage());
            map.put(" rootPath", dockerContainer.getRootPath());
            map.put(" runCmd", dockerContainer.getRunCmd());
            map.put(" status", dockerContainer.getStatus());
            map.put(" serverId", dockerContainer.getDockerServer().getId());
            map.put(" environmentVerId", dockerContainer.getEnvironmentVerId());
            map.put(" lspPort", dockerContainer.getLspPort());
            map.put(" envMap", dockerContainer.getEnvMap());
            map.put(" tenantId", dockerContainer.getTenantId());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

}