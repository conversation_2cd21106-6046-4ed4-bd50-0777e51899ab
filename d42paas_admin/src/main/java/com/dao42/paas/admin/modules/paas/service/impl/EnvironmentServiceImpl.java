package com.dao42.paas.admin.modules.paas.service.impl;

import com.dao42.paas.admin.modules.paas.service.EnvironmentService;
import com.dao42.paas.admin.modules.paas.service.dto.EnvironmentDto;
import com.dao42.paas.admin.modules.paas.service.mapstruct.EnvironmentMapper;
import com.dao42.paas.admin.modules.paas.service.query.EnvironmentQueryCriteria;
import com.dao42.paas.admin.utils.FileUtil;
import com.dao42.paas.admin.utils.PageUtil;
import com.dao42.paas.admin.utils.QueryHelp;
import com.dao42.paas.admin.utils.ValidationUtil;
import com.dao42.paas.model.Environment;
import com.dao42.paas.repository.EnvironmentRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 服务实现
 **/
@Service
@RequiredArgsConstructor
public class EnvironmentServiceImpl implements EnvironmentService {

    private final EnvironmentRepository environmentRepository;
    private final EnvironmentMapper environmentMapper;

    @Override
    public Map<String, Object> queryAll(EnvironmentQueryCriteria criteria, Pageable pageable) {
        Page<Environment> page = environmentRepository.findAll(
            (root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder),
            pageable);
        return PageUtil.toPage(page.map(environmentMapper::toDto));
    }

    @Override
    public List<EnvironmentDto> queryAll(EnvironmentQueryCriteria criteria) {
        return environmentMapper.toDto(environmentRepository.findAll(
            (root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
    }

    @Override
    @Transactional
    public EnvironmentDto findById(Long id) {
        Environment environment = environmentRepository.findById(id).orElseGet(Environment::new);
        ValidationUtil.isNull(environment.getId(), "Environment", "id", id);
        return environmentMapper.toDto(environment);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public EnvironmentDto create(Environment resources) {

        return environmentMapper.toDto(environmentRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Environment resources) {
        Environment environment = environmentRepository.findById(resources.getId()).orElseGet(Environment::new);
        ValidationUtil.isNull(environment.getId(), "Environment", "id", resources.getId());
        environment.copy(resources);
        environmentRepository.save(environment);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            environmentRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<EnvironmentDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (EnvironmentDto environment : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put(" createdBy", environment.getCreatedBy());
            map.put(" createdDate", environment.getCreatedDate());
            map.put(" lastModifiedBy", environment.getLastModifiedBy());
            map.put(" lastModifiedDate", environment.getLastModifiedDate());
            map.put(" version", environment.getVersion());
            map.put(" appPath", environment.getAppPath());
            map.put(" description", environment.getDescription());
            map.put(" fileTreeIgnore", environment.getFileTreeIgnore());
            map.put(" framework", environment.getFramework());
            map.put(" image", environment.getImage());
            map.put("环境名称", environment.getName());
            map.put(" needBrowser", environment.getNeedBrowser());
            map.put(" needConsole", environment.getNeedConsole());
            map.put(" needRunButton", environment.getNeedRunButton());
            map.put(" port", environment.getPort());
            map.put(" projectDependencyCmd", environment.getProjectDependencyCmd());
            map.put(" uninstallNixCmd", environment.getUninstallNixCmd());
            map.put(" testCaseCmd", environment.getTestCaseCmd());
            map.put(" languageId", environment.getLanguageId());
            map.put(" imageTmp", environment.getImageTmp());
            map.put(" imageootbbase", environment.getImageootbbase());
            map.put("可使用的最大的CPU数", environment.getCpuCount());
            map.put("分配的CPU的使用率。用来计算cpu的负载", environment.getCpuUsedRatio());
            map.put("内存需求MB", environment.getMemorymb());
            map.put("内存最大使用百分比", environment.getMemoryUsedRatio());
            map.put(" shellCmd", environment.getShellCmd());
            map.put(" deleted", environment.getDeleted());
            map.put(" fileTreePackage", environment.getFileTreePackage());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}