package com.dao42.paas.admin.modules.paas.service.mapstruct;

import com.dao42.paas.admin.base.BaseMapper;
import com.dao42.paas.admin.modules.paas.service.dto.EnvironmentDto;
import com.dao42.paas.model.Environment;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 **/
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface EnvironmentMapper extends BaseMapper<EnvironmentDto, Environment> {

}