package com.dao42.paas.admin.modules.paas.service;

import com.dao42.paas.admin.modules.paas.service.dto.DockerServerDto;
import com.dao42.paas.admin.modules.paas.service.query.DockerServerQueryCriteria;
import com.dao42.paas.model.docker.DockerServer;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 服务接口
 **/
public interface DockerServerService {

    /**
     * 查询数据分页
     *
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Map<String, Object> queryAll(DockerServerQueryCriteria criteria, Pageable pageable);

    /**
     * 查询所有数据不分页
     *
     * @param criteria 条件参数
     * @return List<DockerServerDto>
     */
    List<DockerServerDto> queryAll(DockerServerQueryCriteria criteria);

    /**
     * 根据ID查询
     *
     * @param id ID
     * @return DockerServerDto
     */
    DockerServerDto findById(Long id);

    /**
     * 创建
     *
     * @param resources /
     * @return DockerServerDto
     */
    DockerServerDto create(DockerServer resources);

    /**
     * 编辑
     *
     * @param resources /
     */
    void update(DockerServer resources);

    /**
     * 多选删除
     *
     * @param ids /
     */
    void deleteAll(Long[] ids);

    /**
     * 导出数据
     *
     * @param all      待导出的数据
     * @param response /
     * @throws IOException /
     */
    void download(List<DockerServerDto> all, HttpServletResponse response) throws IOException;

    /**
     * 上线
     *
     * @param id id
     */
    void setOnline(Long id);

    /**
     * 下线
     *
     * @param id id
     */
    void setOffline(Long id);

}