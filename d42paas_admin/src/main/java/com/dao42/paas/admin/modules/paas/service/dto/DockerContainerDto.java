package com.dao42.paas.admin.modules.paas.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @description /
 **/
@Data
public class DockerContainerDto implements Serializable {

    /**
     * 防止精度丢失
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    /**
     * Docker ID
     */
    private String containerId;

    /**
     * 容器服务端口绑定的宿主机端口
     */
    private Integer hostPort;

    /**
     * 构建容器的镜像
     */
    private String image;

    /**
     * 容器中代码在宿主机的路径
     */
    private String rootPath;

    /**
     * 运行项目的命令
     */
    private String runCmd;

    /**
     * 容器状态
     */
    private String status;

    /**
     * 所属DockerServer
     */
    private DockerServerDto dockerServer;

    /**
     * 构建docker的基础镜像
     */
    private Long environmentVerId;

    /**
     * 容器lsp服务绑定的宿主机端口
     */
    private Integer lspPort;

    /**
     * 环境变量
     */
    private String envMap;

    /**
     * 租户
     */
    private Long tenantId;

    /**
     * CPU使用率
     */
    private Long cpuUsedPercent;

    /**
     * 内存使用率
     */
    private Long memUsedPercent;

    /**
     * 实际内存使用
     */
    private Long actualMemoryUsage;

    /**
     * 理论内存使用
     */
    private Integer theoryMemoryUsage;

    /**
     * 内存上限（从dockerContainer->envVer->env的 memoryMB 取值）
     */
    private Integer memoryLimit;

    /**
     * 父容器
     */
    private Long parentDockerId;

    private Boolean deleted;

    private Long version;

    private String createdBy;

    private Timestamp createdDate;

    private String lastModifiedBy;

    private Timestamp lastModifiedDate;

}