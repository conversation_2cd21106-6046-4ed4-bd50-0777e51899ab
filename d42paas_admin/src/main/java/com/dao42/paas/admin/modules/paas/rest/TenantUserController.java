package com.dao42.paas.admin.modules.paas.rest;

import com.dao42.paas.admin.annotation.Log;
import com.dao42.paas.admin.modules.paas.service.TenantUserService;
import com.dao42.paas.admin.modules.paas.service.query.TenantUserQueryCriteria;
import com.dao42.paas.model.user.TenantUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 **/
@RestController
@RequiredArgsConstructor
@Api(tags = "租户用户管理")
@RequestMapping("/api/tenantUser")
public class TenantUserController {

    private final TenantUserService tenantUserService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('tenantUser:list')")
    public void exportTenantUser(HttpServletResponse response, TenantUserQueryCriteria criteria) throws IOException {
        tenantUserService.download(tenantUserService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询tenantUser")
    @ApiOperation("查询tenantUser")
    @PreAuthorize("@el.check('tenantUser:list')")
    public ResponseEntity<Object> queryTenantUser(TenantUserQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(tenantUserService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增tenantUser")
    @ApiOperation("新增tenantUser")
    @PreAuthorize("@el.check('tenantUser:add')")
    public ResponseEntity<Object> createTenantUser(@Validated @RequestBody TenantUser resources) {
        return new ResponseEntity<>(tenantUserService.create(resources), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改tenantUser")
    @ApiOperation("修改tenantUser")
    @PreAuthorize("@el.check('tenantUser:edit')")
    public ResponseEntity<Object> updateTenantUser(@Validated @RequestBody TenantUser resources) {
        tenantUserService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除tenantUser")
    @ApiOperation("删除tenantUser")
    @PreAuthorize("@el.check('tenantUser:del')")
    public ResponseEntity<Object> deleteTenantUser(@RequestBody Long[] ids) {
        tenantUserService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}