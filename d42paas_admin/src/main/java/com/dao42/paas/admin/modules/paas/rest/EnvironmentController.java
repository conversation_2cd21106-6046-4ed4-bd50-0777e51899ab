package com.dao42.paas.admin.modules.paas.rest;

import com.dao42.paas.admin.annotation.Log;
import com.dao42.paas.admin.modules.paas.service.EnvironmentService;
import com.dao42.paas.admin.modules.paas.service.query.EnvironmentQueryCriteria;
import com.dao42.paas.model.Environment;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 **/
@RestController
@RequiredArgsConstructor
@Api(tags = "environment管理")
@RequestMapping("/api/environment")
public class EnvironmentController {

    private final EnvironmentService environmentService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('environment:list')")
    public void exportEnvironment(HttpServletResponse response, EnvironmentQueryCriteria criteria) throws IOException {
        environmentService.download(environmentService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询environment")
    @ApiOperation("查询environment")
    @PreAuthorize("@el.check('environment:list')")
    public ResponseEntity<Object> queryEnvironment(EnvironmentQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(environmentService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增environment")
    @ApiOperation("新增environment")
    @PreAuthorize("@el.check('environment:add')")
    public ResponseEntity<Object> createEnvironment(@Validated @RequestBody Environment resources) {
        return new ResponseEntity<>(environmentService.create(resources), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改environment")
    @ApiOperation("修改environment")
    @PreAuthorize("@el.check('environment:edit')")
    public ResponseEntity<Object> updateEnvironment(@Validated @RequestBody Environment resources) {
        environmentService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除environment")
    @ApiOperation("删除environment")
    @PreAuthorize("@el.check('environment:del')")
    public ResponseEntity<Object> deleteEnvironment(@RequestBody Long[] ids) {
        environmentService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}