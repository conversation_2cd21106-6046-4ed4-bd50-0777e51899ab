package com.dao42.paas.admin.modules.paas.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @description /
 **/
@Data
public class EnvironmentDto implements Serializable {

    /**
     * 防止精度丢失
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    private String appPath;

    private String description;

    private String fileTreeIgnore;

    private String framework;

    private String image;

    /**
     * 环境名称
     */
    private String name;

    private Boolean needBrowser;

    private Boolean needConsole;

    private Boolean needRunButton;

    private Integer port;

    private String projectDependencyCmd;

    private String uninstallNixCmd;

    private String testCaseCmd;

    private Long languageId;

    private String imageTmp;

    private String imageootbbase;

    /**
     * 可使用的最大的CPU数
     */
    private Integer cpuCount;

    /**
     * 分配的CPU的使用率。用来计算cpu的负载
     */
    private Integer cpuUsedRatio;

    /**
     * 内存需求MB
     */
    private Integer memorymb;

    /**
     * 内存最大使用百分比
     */
    private Integer memoryUsedRatio;

    private String shellCmd;

    private Boolean deleted;

    private String fileTreePackage;

    private String createdBy;

    private Timestamp createdDate;

    private String lastModifiedBy;

    private Timestamp lastModifiedDate;

    private Long version;
}