package com.dao42.paas.admin.modules.paas.rest;

import com.dao42.paas.admin.annotation.Log;
import com.dao42.paas.admin.modules.paas.service.DockerContainerService;
import com.dao42.paas.admin.modules.paas.service.query.DockerContainerQueryCriteria;
import com.dao42.paas.model.docker.DockerContainer;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 **/
@RestController
@RequiredArgsConstructor
@Api(tags = "dockerContainer管理")
@RequestMapping("/api/dockerContainer")
public class DockerContainerController {

    private final DockerContainerService dockerContainerService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('dockerContainer:list')")
    public void exportDockerContainer(HttpServletResponse response, DockerContainerQueryCriteria criteria) throws IOException {
        dockerContainerService.download(dockerContainerService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询dockerContainer")
    @ApiOperation("查询dockerContainer")
    @PreAuthorize("@el.check('dockerContainer:list')")
    public ResponseEntity
        <Object> queryDockerContainer(DockerContainerQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(dockerContainerService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增dockerContainer")
    @ApiOperation("新增dockerContainer")
    @PreAuthorize("@el.check('dockerContainer:add')")
    public ResponseEntity
        <Object> createDockerContainer(@Validated @RequestBody DockerContainer resources) {
        return new ResponseEntity<>(dockerContainerService.create(resources), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改dockerContainer")
    @ApiOperation("修改dockerContainer")
    @PreAuthorize("@el.check('dockerContainer:edit')")
    public ResponseEntity
        <Object> updateDockerContainer(@Validated @RequestBody DockerContainer resources) {
        dockerContainerService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除dockerContainer")
    @ApiOperation("删除dockerContainer")
    @PreAuthorize("@el.check('dockerContainer:del')")
    public ResponseEntity
        <Object> deleteDockerContainer(@RequestBody Long[] ids) {
        dockerContainerService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}