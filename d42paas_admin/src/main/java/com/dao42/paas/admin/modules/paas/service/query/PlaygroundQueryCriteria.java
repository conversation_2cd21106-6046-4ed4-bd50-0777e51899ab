package com.dao42.paas.admin.modules.paas.service.query;

import com.dao42.paas.admin.annotation.Query;
import com.dao42.paas.common.enums.PlaygroundStatus;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Data
public class PlaygroundQueryCriteria {

    @Query(type = Query.Type.RIGHT_LIKE)
    private String id;

    @Query(type = Query.Type.RIGHT_LIKE)
    private String bindObjectId;

    @Query(type = Query.Type.RIGHT_LIKE, propName = "id" ,joinName = "dockerContainer")
    private String dockerContainerId;

    @Query(type = Query.Type.EQUAL)
    private PlaygroundStatus status;

    @Query(type = Query.Type.BETWEEN)
    private List<Timestamp> createdDate;

}