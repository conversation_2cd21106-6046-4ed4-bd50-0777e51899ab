package com.dao42.paas.admin.modules.paas.service.impl;

import com.dao42.paas.admin.modules.paas.service.PlaygroundService;
import com.dao42.paas.admin.modules.paas.service.dto.PlaygroundDto;
import com.dao42.paas.admin.modules.paas.service.mapstruct.PlaygroundMapper;
import com.dao42.paas.admin.modules.paas.service.query.PlaygroundQueryCriteria;
import com.dao42.paas.admin.utils.FileUtil;
import com.dao42.paas.admin.utils.PageUtil;
import com.dao42.paas.admin.utils.QueryHelp;
import com.dao42.paas.admin.utils.ValidationUtil;
import com.dao42.paas.model.Playground;
import com.dao42.paas.repository.PlaygroundRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 服务实现
 **/
@RequiredArgsConstructor
@Service
public class PlaygroundServiceImpl implements PlaygroundService {

    private final PlaygroundRepository playgroundRepository;
    private final PlaygroundMapper playgroundMapper;

    @Override
    public Map<String, Object> queryAll(PlaygroundQueryCriteria criteria, Pageable pageable) {
        Page<Playground> page = playgroundRepository.findAll(
            (root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder),
            pageable);
        return PageUtil.toPage(page.map(playgroundMapper::toDto));
    }

    @Override
    public List<PlaygroundDto> queryAll(PlaygroundQueryCriteria criteria) {
        return playgroundMapper.toDto(playgroundRepository.findAll(
            (root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
    }

    @Override
    @Transactional
    public PlaygroundDto findById(Long id) {
        Playground playground = playgroundRepository.findById(id).orElseGet(Playground::new);
        ValidationUtil.isNull(playground.getId(), "Playground", "id", id);
        return playgroundMapper.toDto(playground);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PlaygroundDto create(Playground resources) {

        return playgroundMapper.toDto(playgroundRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Playground resources) {
        Playground playground = playgroundRepository.findById(resources.getId()).orElseGet(Playground::new);
        ValidationUtil.isNull(playground.getId(), "Playground", "id", resources.getId());
        playground.copy(resources);
        playgroundRepository.save(playground);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            playgroundRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<PlaygroundDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (PlaygroundDto playground : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("创建人", playground.getCreatedBy());
            map.put("创建时间", playground.getCreatedDate());
            map.put("最后修改人", playground.getLastModifiedBy());
            map.put("最后更新时间", playground.getLastModifiedDate());
            map.put("版本", playground.getVersion());
            map.put("绑定对象", playground.getBindObjectId());
            map.put("绑定对象类型", playground.getBindType());
            map.put("是否删除", playground.getDeleted());
            map.put("状态", playground.getStatus());
            map.put("租户", playground.getTenant());
            map.put("绑定的容器", playground.getDockerContainer().getContainerId());
            map.put("绑定的IDE Server", playground.getIdeServer().getCode());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}