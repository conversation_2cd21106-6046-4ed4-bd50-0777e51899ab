package com.dao42.paas.admin.modules.paas.rest;

import com.dao42.paas.admin.annotation.Log;
import com.dao42.paas.admin.modules.paas.service.DockerServerService;
import com.dao42.paas.admin.modules.paas.service.query.DockerServerQueryCriteria;
import com.dao42.paas.model.docker.DockerServer;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 **/
@RestController
@RequiredArgsConstructor
@Api(tags = "dockerServer管理")
@RequestMapping("/api/dockerServer")
public class DockerServerController {

    private final DockerServerService dockerServerService;

    @GetMapping
    @Log("查询dockerServer")
    @ApiOperation("查询dockerServer")
    @PreAuthorize("@el.check('dockerServer:list')")
    public ResponseEntity<Object> queryDockerServer(DockerServerQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(dockerServerService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增dockerServer")
    @ApiOperation("新增dockerServer")
    @PreAuthorize("@el.check('dockerServer:add')")
    public ResponseEntity<Object> createDockerServer(@Validated @RequestBody DockerServer resources) {
        dockerServerService.create(resources);
        return new ResponseEntity<>(HttpStatus.CREATED);
    }

    @PutMapping()
    @Log("修改dockerServer")
    @ApiOperation("修改dockerServer")
    @PreAuthorize("@el.check('dockerServer:edit')")
    public ResponseEntity<Object> updateDockerServer(@Validated @RequestBody DockerServer resources) {
        dockerServerService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PutMapping("/online")
    @Log("上线DockerServer")
    @ApiOperation("上线DockerServer")
    @PreAuthorize("@el.check('dockerServer:online')")
    public ResponseEntity<Object> setOnline(@RequestBody List<Long> ids) {
        ids.forEach(dockerServerService::setOnline);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PutMapping("/offline")
    @Log("下线dockerServer")
    @ApiOperation("下线dockerServer")
    @PreAuthorize("@el.check('dockerServer:offline')")
    public ResponseEntity<Object> setOffline(@RequestBody List<Long> ids) {
        ids.forEach(dockerServerService::setOffline);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除dockerServer")
    @ApiOperation("删除dockerServer")
    @PreAuthorize("@el.check('dockerServer:del')")
    public ResponseEntity<Object> deleteDockerServer(@RequestBody Long[] ids) {
        dockerServerService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

}