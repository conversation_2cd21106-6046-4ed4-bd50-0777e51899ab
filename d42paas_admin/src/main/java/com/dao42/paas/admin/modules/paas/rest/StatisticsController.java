package com.dao42.paas.admin.modules.paas.rest;

import com.dao42.paas.admin.modules.paas.service.StatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "统计信息")
@RequestMapping("/api/statistics")
public class StatisticsController {

    private final StatisticsService service;

    @ApiOperation("查询统计数据")
    @GetMapping
    public Map<String, Object> get(@ApiParam("指定天数") @RequestParam(defaultValue = "7") Integer days) {
        return service.getPreviousDays(days);
    }

    @ApiOperation("生成统计数据")
    @GetMapping("/generate")
    public ResponseEntity<Object> generateRecentDays(@ApiParam("指定天数") @RequestParam(defaultValue = "7") Integer days) {
        service.generateRecentDays(days);
        return ResponseEntity.ok().build();
    }

}
