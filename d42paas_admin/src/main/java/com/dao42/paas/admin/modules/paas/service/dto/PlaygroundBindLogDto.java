package com.dao42.paas.admin.modules.paas.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 **/
@Data
public class PlaygroundBindLogDto implements Serializable {

    /**
     * 防止精度丢失
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    private String createdBy;

    private Timestamp createdDate;

    private String lastModifiedBy;

    private Timestamp lastModifiedDate;

    private Long version;

    /**
     * 绑定对象的ID
     */
    private Long bindObjectId;

    /**
     * 容器绑定playground的时间
     */
    private Timestamp bindTime;

    /**
     * 绑定对象的类型
     */
    private String bindType;

    /**
     * 容器ID
     */
    private Long dockerContainerId;

    /**
     * playground
     */
    private Long playgroundId;
}