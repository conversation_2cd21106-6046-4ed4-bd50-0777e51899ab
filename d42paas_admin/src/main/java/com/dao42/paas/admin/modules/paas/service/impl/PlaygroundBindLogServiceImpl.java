package com.dao42.paas.admin.modules.paas.service.impl;

import com.dao42.paas.admin.modules.paas.service.PlaygroundBindLogService;
import com.dao42.paas.admin.modules.paas.service.dto.PlaygroundBindLogDto;
import com.dao42.paas.admin.modules.paas.service.mapstruct.PlaygroundBindLogMapper;
import com.dao42.paas.admin.modules.paas.service.query.PlaygroundBindLogQueryCriteria;
import com.dao42.paas.admin.utils.FileUtil;
import com.dao42.paas.admin.utils.PageUtil;
import com.dao42.paas.admin.utils.QueryHelp;
import com.dao42.paas.admin.utils.ValidationUtil;
import com.dao42.paas.model.PlaygroundBindLog;
import com.dao42.paas.repository.PlaygroundBindLogRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 **/
@Service
@RequiredArgsConstructor
public class PlaygroundBindLogServiceImpl implements PlaygroundBindLogService {

    private final PlaygroundBindLogRepository playgroundBindLogRepository;
    private final PlaygroundBindLogMapper playgroundBindLogMapper;

    @Override
    public Map<String, Object> queryAll(PlaygroundBindLogQueryCriteria criteria, Pageable pageable) {
        Page<PlaygroundBindLog> page = playgroundBindLogRepository.findAll(
            (root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder),
            pageable);
        return PageUtil.toPage(page.map(playgroundBindLogMapper::toDto));
    }

    @Override
    public List<PlaygroundBindLogDto> queryAll(PlaygroundBindLogQueryCriteria criteria) {
        return playgroundBindLogMapper.toDto(playgroundBindLogRepository.findAll(
            (root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
    }

    @Override
    @Transactional
    public PlaygroundBindLogDto findById(Long id) {
        PlaygroundBindLog playgroundBindLog =
            playgroundBindLogRepository.findById(id).orElseGet(PlaygroundBindLog::new);
        ValidationUtil.isNull(playgroundBindLog.getId(), "PlaygroundBindLog", "id", id);
        return playgroundBindLogMapper.toDto(playgroundBindLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PlaygroundBindLogDto create(PlaygroundBindLog resources) {

        return playgroundBindLogMapper.toDto(playgroundBindLogRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(PlaygroundBindLog resources) {
        PlaygroundBindLog playgroundBindLog =
            playgroundBindLogRepository.findById(resources.getId()).orElseGet(PlaygroundBindLog::new);
        ValidationUtil.isNull(playgroundBindLog.getId(), "PlaygroundBindLog", "id", resources.getId());
        playgroundBindLog.copy(resources);
        playgroundBindLogRepository.save(playgroundBindLog);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            playgroundBindLogRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<PlaygroundBindLogDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (PlaygroundBindLogDto playgroundBindLog : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put(" createdBy", playgroundBindLog.getCreatedBy());
            map.put(" createdDate", playgroundBindLog.getCreatedDate());
            map.put(" lastModifiedBy", playgroundBindLog.getLastModifiedBy());
            map.put(" lastModifiedDate", playgroundBindLog.getLastModifiedDate());
            map.put(" version", playgroundBindLog.getVersion());
            map.put("绑定对象的ID", playgroundBindLog.getBindObjectId());
            map.put("容器绑定playground的时间", playgroundBindLog.getBindTime());
            map.put("绑定对象的类型", playgroundBindLog.getBindType());
            map.put("容器ID", playgroundBindLog.getDockerContainerId());
            map.put("playground", playgroundBindLog.getPlaygroundId());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}