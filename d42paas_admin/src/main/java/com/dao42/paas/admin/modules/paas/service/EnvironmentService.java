package com.dao42.paas.admin.modules.paas.service;

import com.dao42.paas.admin.modules.paas.service.dto.EnvironmentDto;
import com.dao42.paas.admin.modules.paas.service.query.EnvironmentQueryCriteria;
import com.dao42.paas.model.Environment;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 服务接口
 **/
public interface EnvironmentService {

    /**
     * 查询数据分页
     *
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Map<String, Object> queryAll(EnvironmentQueryCriteria criteria, Pageable pageable);

    /**
     * 查询所有数据不分页
     *
     * @param criteria 条件参数
     * @return List<EnvironmentDto>
     */
    List<EnvironmentDto> queryAll(EnvironmentQueryCriteria criteria);

    /**
     * 根据ID查询
     *
     * @param id ID
     * @return EnvironmentDto
     */
    EnvironmentDto findById(Long id);

    /**
     * 创建
     *
     * @param resources /
     * @return EnvironmentDto
     */
    EnvironmentDto create(Environment resources);

    /**
     * 编辑
     *
     * @param resources /
     */
    void update(Environment resources);

    /**
     * 多选删除
     *
     * @param ids /
     */
    void deleteAll(Long[] ids);

    /**
     * 导出数据
     *
     * @param all      待导出的数据
     * @param response /
     * @throws IOException /
     */
    void download(List<EnvironmentDto> all, HttpServletResponse response) throws IOException;
}