package com.dao42.paas.admin.modules.paas.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Data
public class PlaygroundDto implements Serializable {

    /* ID */
    /**
     * 防止精度丢失
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    /**
     * 绑定对象
     */
    private Long bindObjectId;

    /**
     * 绑定对象类型
     */
    private String bindType;

    /**
     * 是否删除
     */
    private Boolean deleted;

    /**
     * 状态
     */
    private String status;

    /**
     * 租户
     */
    private TenantDto tenant;

    /**
     * 绑定的容器
     */
    private DockerContainerDto dockerContainer;

    /**
     * 下属的中间件列表
     */
    private List<MiddlewareInstanceDto> middlewares;

    /**
     * 绑定的IDE Server
     */
    private IDEServerDto ideServer;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Timestamp createdDate;

    /**
     * 最后修改人
     */
    private String lastModifiedBy;

    /**
     * 最后更新时间
     */
    private Timestamp lastModifiedDate;

    /**
     * 版本
     */
    private Long version;

}