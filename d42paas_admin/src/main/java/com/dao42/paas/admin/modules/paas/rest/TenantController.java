package com.dao42.paas.admin.modules.paas.rest;

import com.dao42.paas.admin.annotation.Log;
import com.dao42.paas.admin.modules.paas.service.TenantService;
import com.dao42.paas.admin.modules.paas.service.query.TenantQueryCriteria;
import com.dao42.paas.model.tenant.Tenant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 **/
@RestController
@RequiredArgsConstructor
@Api(tags = "租户管理")
@RequestMapping("/api/tenant")
public class TenantController {

    private final TenantService tenantService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('tenant:list')")
    public void exportTenant(HttpServletResponse response, TenantQueryCriteria criteria) throws IOException {
        tenantService.download(tenantService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询tenant")
    @ApiOperation("查询tenant")
    @PreAuthorize("@el.check('tenant:list')")
    public ResponseEntity<Object> queryTenant(TenantQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(tenantService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增tenant")
    @ApiOperation("新增tenant")
    @PreAuthorize("@el.check('tenant:add')")
    public ResponseEntity<Object> createTenant(@Validated @RequestBody Tenant resources) {
        return new ResponseEntity<>(tenantService.create(resources), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改tenant")
    @ApiOperation("修改tenant")
    @PreAuthorize("@el.check('tenant:edit')")
    public ResponseEntity<Object> updateTenant(@Validated @RequestBody Tenant resources) {
        tenantService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除tenant")
    @ApiOperation("删除tenant")
    @PreAuthorize("@el.check('tenant:del')")
    public ResponseEntity<Object> deleteTenant(@RequestBody Long[] ids) {
        tenantService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}