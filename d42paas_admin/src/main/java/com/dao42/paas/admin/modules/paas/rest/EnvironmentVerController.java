package com.dao42.paas.admin.modules.paas.rest;

import com.dao42.paas.admin.annotation.Log;
import com.dao42.paas.admin.modules.paas.service.EnvironmentVerService;
import com.dao42.paas.admin.modules.paas.service.query.EnvironmentVerQueryCriteria;
import com.dao42.paas.model.EnvironmentVer;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 **/
@RestController
@RequiredArgsConstructor
@Api(tags = "environmentVer管理")
@RequestMapping("/api/environmentVer")
public class EnvironmentVerController {

    private final EnvironmentVerService environmentVerService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('environmentVer:list')")
    public void exportEnvironmentVer(HttpServletResponse response, EnvironmentVerQueryCriteria criteria) throws IOException {
        environmentVerService.download(environmentVerService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询environmentVer")
    @ApiOperation("查询environmentVer")
    @PreAuthorize("@el.check('environmentVer:list')")
    public ResponseEntity<Object> queryEnvironmentVer(EnvironmentVerQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(environmentVerService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增environmentVer")
    @ApiOperation("新增environmentVer")
    @PreAuthorize("@el.check('environmentVer:add')")
    public ResponseEntity<Object> createEnvironmentVer(@Validated @RequestBody EnvironmentVer resources) {
        return new ResponseEntity<>(environmentVerService.create(resources), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改environmentVer")
    @ApiOperation("修改environmentVer")
    @PreAuthorize("@el.check('environmentVer:edit')")
    public ResponseEntity<Object> updateEnvironmentVer(@Validated @RequestBody EnvironmentVer resources) {
        environmentVerService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除environmentVer")
    @ApiOperation("删除environmentVer")
    @PreAuthorize("@el.check('environmentVer:del')")
    public ResponseEntity<Object> deleteEnvironmentVer(@RequestBody Long[] ids) {
        environmentVerService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}