package com.dao42.paas.admin.modules.paas.rest;

import com.dao42.paas.admin.annotation.Log;
import com.dao42.paas.admin.modules.paas.service.IDEServerService;
import com.dao42.paas.admin.modules.paas.service.query.IDEServerQueryCriteria;
import com.dao42.paas.model.IDEServer;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 **/
@RestController
@RequiredArgsConstructor
@Api(tags = "ideServer管理")
@RequestMapping("/api/ideServer")
public class IDEServerController {

    private final IDEServerService ideServerService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('ideServer:list')")
    public void exportIDEServer(HttpServletResponse response, IDEServerQueryCriteria criteria) throws IOException {
        ideServerService.download(ideServerService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询ideServer")
    @ApiOperation("查询ideServer")
    @PreAuthorize("@el.check('ideServer:list')")
    public ResponseEntity<Object> queryIDEServer(IDEServerQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(ideServerService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增ideServer")
    @ApiOperation("新增ideServer")
    @PreAuthorize("@el.check('ideServer:add')")
    public ResponseEntity<Object> createIDEServer(@Validated @RequestBody IDEServer resources) {
        return new ResponseEntity<>(ideServerService.create(resources), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改ideServer")
    @ApiOperation("修改ideServer")
    @PreAuthorize("@el.check('ideServer:edit')")
    public ResponseEntity<Object> updateIDEServer(@Validated @RequestBody IDEServer resources) {
        ideServerService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除ideServer")
    @ApiOperation("删除ideServer")
    @PreAuthorize("@el.check('ideServer:del')")
    public ResponseEntity<Object> deleteIDEServer(@RequestBody Long[] ids) {
        ideServerService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}