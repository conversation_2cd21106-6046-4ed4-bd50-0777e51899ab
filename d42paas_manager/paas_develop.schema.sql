/*
 Navicat Premium Dump SQL

 Source Server         : Clacky-Manager-<PERSON>elop
 Source Server Type    : MySQL
 Source Server Version : 80041 (8.0.41)
 Source Host           : paas-mysql.czc8u2ykg73t.us-east-1.rds.amazonaws.com:3306
 Source Schema         : paas_develop

 Target Server Type    : MySQL
 Target Server Version : 80041 (8.0.41)
 File Encoding         : 65001

 Date: 27/05/2025 18:20:56
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;
create database if not exists paas_develop;
use paas_develop;
-- ----------------------------
-- Table structure for code_zone
-- ----------------------------
DROP TABLE IF EXISTS `code_zone`;
CREATE TABLE `code_zone` (
  `id` bigint NOT NULL,
  `deleted` bit(1) NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `resources_limit` json DEFAULT NULL,
  `root_path` varchar(255) DEFAULT NULL,
  `tenant_id` bigint DEFAULT NULL,
  `user_id` varchar(255) DEFAULT NULL,
  `environment_ver_id` bigint NOT NULL,
  `unit_test_framework_id` bigint DEFAULT NULL,
  `env` json DEFAULT NULL,
  `purpose` varchar(255) DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FK3398u21u1turyhvt7uut0sjbe` (`environment_ver_id`) USING BTREE,
  KEY `FKi0s0b5p17xs596npatsgi5h1k` (`unit_test_framework_id`) USING BTREE,
  CONSTRAINT `FK3398u21u1turyhvt7uut0sjbe` FOREIGN KEY (`environment_ver_id`) REFERENCES `environment_ver` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKi0s0b5p17xs596npatsgi5h1k` FOREIGN KEY (`unit_test_framework_id`) REFERENCES `unit_test_framework` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for code_zone_disk_resource
-- ----------------------------
DROP TABLE IF EXISTS `code_zone_disk_resource`;
CREATE TABLE `code_zone_disk_resource` (
  `id` bigint NOT NULL,
  `deleted` bit(1) NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `code_zone_id` bigint NOT NULL,
  `disk_resource_id` bigint NOT NULL,
  `mount_path` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `UKt1fq3s8jilc6g7fqykpmmy81o` (`code_zone_id`,`disk_resource_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for code_zone_middleware_config_list
-- ----------------------------
DROP TABLE IF EXISTS `code_zone_middleware_config_list`;
CREATE TABLE `code_zone_middleware_config_list` (
  `code_zone_id` bigint NOT NULL,
  `middleware_config_list_id` bigint NOT NULL,
  UNIQUE KEY `UK_aa9dwy793hktpfh4f3oa6ncvd` (`middleware_config_list_id`) USING BTREE,
  KEY `FKeq6wafe56olskkj80iqxgk0eb` (`code_zone_id`) USING BTREE,
  CONSTRAINT `FKbnhfbdwaya83jee3qrqn2ostt` FOREIGN KEY (`middleware_config_list_id`) REFERENCES `middleware_config` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKeq6wafe56olskkj80iqxgk0eb` FOREIGN KEY (`code_zone_id`) REFERENCES `code_zone` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for code_zone_snapshot
-- ----------------------------
DROP TABLE IF EXISTS `code_zone_snapshot`;
CREATE TABLE `code_zone_snapshot` (
  `id` bigint NOT NULL,
  `deleted` bit(1) NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `commit_id` varchar(255) DEFAULT NULL,
  `path` varchar(255) DEFAULT NULL,
  `resources_limit` json DEFAULT NULL,
  `unit_test_framework_name` varchar(255) DEFAULT NULL,
  `code_zone_id` bigint NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FK7m16qscg8to4nn7qky0c8voue` (`code_zone_id`) USING BTREE,
  CONSTRAINT `FK7m16qscg8to4nn7qky0c8voue` FOREIGN KEY (`code_zone_id`) REFERENCES `code_zone` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for code_zone_snapshot_fork_job
-- ----------------------------
DROP TABLE IF EXISTS `code_zone_snapshot_fork_job`;
CREATE TABLE `code_zone_snapshot_fork_job` (
  `id` bigint NOT NULL,
  `deleted` bit(1) NOT NULL,
  `created_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `code_zone_snapshot_id` bigint NOT NULL,
  `number` int NOT NULL,
  `trigger_time` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for code_zone_snapshot_middleware_config_list
-- ----------------------------
DROP TABLE IF EXISTS `code_zone_snapshot_middleware_config_list`;
CREATE TABLE `code_zone_snapshot_middleware_config_list` (
  `code_zone_snapshot_id` bigint NOT NULL,
  `middleware_config_list_id` bigint NOT NULL,
  KEY `FKhx3x60e7d3450ui7n6trxu90c` (`middleware_config_list_id`) USING BTREE,
  KEY `FKpcyp55l7t49ugabhs9t8bgwq` (`code_zone_snapshot_id`) USING BTREE,
  CONSTRAINT `FKhx3x60e7d3450ui7n6trxu90c` FOREIGN KEY (`middleware_config_list_id`) REFERENCES `middleware_config` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKpcyp55l7t49ugabhs9t8bgwq` FOREIGN KEY (`code_zone_snapshot_id`) REFERENCES `code_zone_snapshot` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for code_zoneurlresource
-- ----------------------------
DROP TABLE IF EXISTS `code_zoneurlresource`;
CREATE TABLE `code_zoneurlresource` (
  `id` bigint NOT NULL,
  `deleted` bit(1) NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `code_zone_id` bigint NOT NULL,
  `url_resource_id` bigint NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `UKpc7f3r5hmq9s8bs8oabmafm36` (`code_zone_id`,`url_resource_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for cos_file
-- ----------------------------
DROP TABLE IF EXISTS `cos_file`;
CREATE TABLE `cos_file` (
  `id` bigint NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `deleted` bit(1) DEFAULT NULL,
  `original_code_type` varchar(255) DEFAULT NULL,
  `code_type` varchar(255) DEFAULT NULL,
  `original_code_id` bigint DEFAULT NULL,
  `code_id` bigint DEFAULT NULL,
  `root_path` varchar(255) DEFAULT NULL,
  `status` varchar(255) DEFAULT NULL COMMENT 'two states: 1 PREPARING 2 READY',
  `ref_count` bigint DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `code_id` (`code_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for disk_resource
-- ----------------------------
DROP TABLE IF EXISTS `disk_resource`;
CREATE TABLE `disk_resource` (
  `id` bigint NOT NULL,
  `deleted` bit(1) NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `maxmb` int NOT NULL,
  `path` varchar(255) DEFAULT NULL,
  `tenant_id` bigint NOT NULL,
  `usedmb` int NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for docker_container
-- ----------------------------
DROP TABLE IF EXISTS `docker_container`;
CREATE TABLE `docker_container` (
  `id` bigint NOT NULL,
  `deleted` bit(1) NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `container_id` varchar(255) DEFAULT NULL,
  `env_map` varchar(255) DEFAULT NULL,
  `host_port` int NOT NULL,
  `image` varchar(1000) DEFAULT NULL,
  `lsp_port` int NOT NULL,
  `pre_create` bit(1) NOT NULL,
  `resources_limit` json DEFAULT NULL,
  `root_path` varchar(255) DEFAULT NULL,
  `status` varchar(255) NOT NULL,
  `tenant_id` bigint DEFAULT NULL,
  `docker_server_id` bigint DEFAULT NULL,
  `environment_ver_id` bigint NOT NULL,
  `parent_docker_id` bigint DEFAULT NULL,
  `default_open_file` varchar(1024) DEFAULT NULL,
  `status_new` varchar(255) DEFAULT NULL,
  `ssh_port` int DEFAULT NULL,
  `project_web_port` int DEFAULT NULL,
  `docker_address` varchar(20) DEFAULT NULL,
  `agent_server_port` int DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FK1g1ypwxq5e64fn5dtd0ogps7r` (`docker_server_id`) USING BTREE,
  KEY `FKam2taj1mc5ytbefa4x5rocy28` (`environment_ver_id`) USING BTREE,
  KEY `FKqwaef7cgoy96ltpsivqlyj2fk` (`parent_docker_id`) USING BTREE,
  KEY `index_0` (`deleted`,`status`) USING BTREE,
  CONSTRAINT `FK1g1ypwxq5e64fn5dtd0ogps7r` FOREIGN KEY (`docker_server_id`) REFERENCES `docker_server` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKam2taj1mc5ytbefa4x5rocy28` FOREIGN KEY (`environment_ver_id`) REFERENCES `environment_ver` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKqwaef7cgoy96ltpsivqlyj2fk` FOREIGN KEY (`parent_docker_id`) REFERENCES `docker_container` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for docker_container_middlewares
-- ----------------------------
DROP TABLE IF EXISTS `docker_container_middlewares`;
CREATE TABLE `docker_container_middlewares` (
  `docker_container_id` bigint NOT NULL,
  `middlewares_id` bigint NOT NULL,
  UNIQUE KEY `UK_6e3ws9yj25wrht8ddfl2eedf6` (`middlewares_id`) USING BTREE,
  UNIQUE KEY `UK_sntewqupnp8b9iej5hgl3qscx` (`middlewares_id`) USING BTREE,
  KEY `FKwklocnh00565peg87dc6h3n2` (`docker_container_id`) USING BTREE,
  CONSTRAINT `FK5yiawgstnobdr41aphmtdba4i` FOREIGN KEY (`middlewares_id`) REFERENCES `middleware_instance` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKgxqxykrjehowpc5o0v6hk270h` FOREIGN KEY (`docker_container_id`) REFERENCES `docker_container` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKr4mctjwm6kyobu5hkmjhxra9t` FOREIGN KEY (`middlewares_id`) REFERENCES `middleware_instance` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKwklocnh00565peg87dc6h3n2` FOREIGN KEY (`docker_container_id`) REFERENCES `docker_container` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for docker_container_recover_log
-- ----------------------------
DROP TABLE IF EXISTS `docker_container_recover_log`;
CREATE TABLE `docker_container_recover_log` (
  `id` bigint NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `container_id` varchar(255) DEFAULT NULL,
  `image` varchar(1000) DEFAULT NULL,
  `docker_container_id` bigint DEFAULT NULL,
  `deleted` bit(1) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FKng4fup19eu2fppdjq2cy5lu7` (`docker_container_id`) USING BTREE,
  CONSTRAINT `FKng4fup19eu2fppdjq2cy5lu7` FOREIGN KEY (`docker_container_id`) REFERENCES `docker_container` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for docker_disk_resource
-- ----------------------------
DROP TABLE IF EXISTS `docker_disk_resource`;
CREATE TABLE `docker_disk_resource` (
  `id` bigint NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `docker_container_id` bigint NOT NULL,
  `mount_path` varchar(255) DEFAULT NULL,
  `resource_id` bigint NOT NULL,
  `deleted` bit(1) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `IDX3cm71wcmrdykssuwsig5d7j7s` (`docker_container_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for docker_images
-- ----------------------------
DROP TABLE IF EXISTS `docker_images`;
CREATE TABLE `docker_images` (
  `id` bigint NOT NULL,
  `deleted` bit(1) NOT NULL,
  `created_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `image_code` varchar(255) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT 'docker镜像唯一编码',
  `image` varchar(255) COLLATE utf8mb4_general_ci DEFAULT '',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `unique_image_code` (`image_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='docker镜像';

-- ----------------------------
-- Table structure for docker_server
-- ----------------------------
DROP TABLE IF EXISTS `docker_server`;
CREATE TABLE `docker_server` (
  `id` bigint NOT NULL,
  `need_ca` bit(1) NOT NULL,
  `cpu_count` bigint NOT NULL,
  `docker_ca_path` varchar(255) DEFAULT NULL,
  `ip` varchar(255) DEFAULT NULL,
  `memorymb` bigint NOT NULL,
  `public_ip` varchar(255) DEFAULT NULL,
  `status` varchar(255) NOT NULL,
  `temp` bit(1) NOT NULL,
  `deleted` bit(1) NOT NULL,
  `instance_info` json DEFAULT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `instance_id` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for docker_server_duty_list
-- ----------------------------
DROP TABLE IF EXISTS `docker_server_duty_list`;
CREATE TABLE `docker_server_duty_list` (
  `docker_server_id` bigint NOT NULL,
  `duty_list` varchar(255) DEFAULT NULL,
  KEY `FK4h98v5900e74n1umiq7ujpjxo` (`docker_server_id`) USING BTREE,
  CONSTRAINT `FK4h98v5900e74n1umiq7ujpjxo` FOREIGN KEY (`docker_server_id`) REFERENCES `docker_server` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for docker_url_resource
-- ----------------------------
DROP TABLE IF EXISTS `docker_url_resource`;
CREATE TABLE `docker_url_resource` (
  `id` bigint NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `docker_container_id` bigint NOT NULL,
  `dockerurlresource_type` varchar(255) NOT NULL,
  `resource_id` bigint NOT NULL,
  `deleted` bit(1) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `IDXkqe9x50x52ett4qpaq2qag7r2` (`docker_container_id`) USING BTREE,
  KEY `UKt0ksq1s9sgqt9sqjqre48xn2f` (`docker_container_id`,`dockerurlresource_type`) USING BTREE,
  KEY `dockerId_type` (`docker_container_id`,`dockerurlresource_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for environment
-- ----------------------------
DROP TABLE IF EXISTS `environment`;
CREATE TABLE `environment` (
  `id` bigint NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `cpu_count` int NOT NULL,
  `cpu_used_ratio` int NOT NULL,
  `deleted` bit(1) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `file_tree_ignore` varchar(255) DEFAULT NULL,
  `file_tree_package` varchar(255) DEFAULT NULL,
  `framework` varchar(255) DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `memorymb` int NOT NULL,
  `memory_used_ratio` int NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `language_package` varchar(255) DEFAULT '',
  `port` int DEFAULT NULL,
  `shell_cmd` varchar(255) DEFAULT NULL,
  `language_id` bigint NOT NULL,
  `resources_limit` json DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FKxbpo6algnlnevs70p7j55bfw` (`language_id`) USING BTREE,
  CONSTRAINT `FKxbpo6algnlnevs70p7j55bfw` FOREIGN KEY (`language_id`) REFERENCES `language` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for environment_ver
-- ----------------------------
DROP TABLE IF EXISTS `environment_ver`;
CREATE TABLE `environment_ver` (
  `id` bigint NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `default_ver` bit(1) NOT NULL,
  `env_code` varchar(255) NOT NULL DEFAULT '',
  `env_ver_key` varchar(255) NOT NULL,
  `framework_version` varchar(255) DEFAULT NULL,
  `language_version` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `environment_id` bigint NOT NULL,
  `deleted` bit(1) NOT NULL,
  `tags` varchar(255) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `frameworks` varchar(255) DEFAULT NULL,
  `languages` varchar(255) DEFAULT NULL,
  `runtime` varchar(255) DEFAULT NULL,
  `runtime_information` varchar(255) DEFAULT NULL,
  `package_managers` varchar(255) DEFAULT NULL,
  `publish` bit(1) DEFAULT NULL,
  `bash_cmd` varchar(1024) DEFAULT NULL,
  `sequence` int DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `unique_env_code` (`env_code`),
  KEY `FK7qkc6p1ng2w0uuv04rg5oe41m` (`environment_id`) USING BTREE,
  CONSTRAINT `FK7qkc6p1ng2w0uuv04rg5oe41m` FOREIGN KEY (`environment_id`) REFERENCES `environment` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for environment_ver_copy1
-- ----------------------------
DROP TABLE IF EXISTS `environment_ver_copy1`;
CREATE TABLE `environment_ver_copy1` (
  `id` bigint NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `default_ver` bit(1) NOT NULL,
  `env_ver_key` varchar(255) NOT NULL,
  `framework_version` varchar(255) DEFAULT NULL,
  `language_version` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `environment_id` bigint NOT NULL,
  `deleted` bit(1) NOT NULL,
  `tools` varchar(255) DEFAULT NULL,
  `tags` varchar(255) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `frameworks` varchar(255) DEFAULT NULL,
  `languages` varchar(255) DEFAULT NULL,
  `runtime` varchar(255) DEFAULT NULL,
  `runtime_information` varchar(255) DEFAULT NULL,
  `package_managers` varchar(255) DEFAULT NULL,
  `publish` bit(1) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FK7qkc6p1ng2w0uuv04rg5oe41m` (`environment_id`) USING BTREE,
  CONSTRAINT `environment_ver_copy1_ibfk_1` FOREIGN KEY (`environment_id`) REFERENCES `environment` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for git_command
-- ----------------------------
DROP TABLE IF EXISTS `git_command`;
CREATE TABLE `git_command` (
  `id` bigint NOT NULL COMMENT 'ID',
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL DEFAULT '0' COMMENT '乐观锁',
  `message_id` varchar(255) DEFAULT '' COMMENT '命令消息唯一编号',
  `playground_id` varchar(255) DEFAULT '' COMMENT 'playground编号',
  `command` varchar(1024) DEFAULT '' COMMENT '执行命令参数',
  `status` tinyint DEFAULT '0' COMMENT '执行命令状态，0：初始化，1： 进行中，2：成功，3：失败',
  `log` text COMMENT '命令log',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='git命令执行记录表';

-- ----------------------------
-- Table structure for ide_server
-- ----------------------------
DROP TABLE IF EXISTS `ide_server`;
CREATE TABLE `ide_server` (
  `id` bigint NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `code` varchar(255) DEFAULT NULL,
  `online` bit(1) NOT NULL,
  `publicurl` varchar(255) DEFAULT NULL,
  `secret` varchar(255) DEFAULT NULL,
  `deleted` bit(1) NOT NULL,
  `test` bit(1) NOT NULL,
  `socket_parser` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `UK_7pxcj7jopmnwkgtidar3plkq2` (`code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for language
-- ----------------------------
DROP TABLE IF EXISTS `language`;
CREATE TABLE `language` (
  `id` bigint NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `console_start_cmd` varchar(1024) DEFAULT NULL,
  `console_supported` bit(1) NOT NULL,
  `lsp_language_id` varchar(255) DEFAULT NULL,
  `lsp_start_cmd` varchar(1024) DEFAULT NULL,
  `lsp_supported` bit(1) NOT NULL,
  `lsp_language_ids` varchar(1024) DEFAULT NULL,
  `lsp_start_cmds` varchar(1024) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `deleted` bit(1) NOT NULL,
  `debug_start_cmd` varchar(1024) DEFAULT NULL,
  `debug_supported` bit(1) NOT NULL,
  `real_time_refresh` bit(1) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for middleware_config
-- ----------------------------
DROP TABLE IF EXISTS `middleware_config`;
CREATE TABLE `middleware_config` (
  `id` bigint NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `docker_launch_env` varchar(255) DEFAULT NULL,
  `user_env` varchar(2048) DEFAULT NULL,
  `define_id` bigint NOT NULL,
  `external_switch` tinyint(1) NOT NULL DEFAULT '0' COMMENT '中间件外网开关，0：开启，1:关闭',
  `deleted` bit(1) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FKgmggfp7yp48p51knpmnuae3e4` (`define_id`) USING BTREE,
  CONSTRAINT `FKgmggfp7yp48p51knpmnuae3e4` FOREIGN KEY (`define_id`) REFERENCES `middleware_define` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for middleware_define
-- ----------------------------
DROP TABLE IF EXISTS `middleware_define`;
CREATE TABLE `middleware_define` (
  `id` bigint NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `middle_type` varchar(255) NOT NULL COMMENT '中间件类型',
  `code` varchar(255) DEFAULT NULL,
  `cpu_count` int NOT NULL,
  `cpu_used_ratio` int NOT NULL,
  `docker_data_path` varchar(255) DEFAULT NULL,
  `docker_service_port` int DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `memorymb` int NOT NULL,
  `memory_used_ratio` int NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `deleted` bit(1) NOT NULL,
  `resources_limit` json DEFAULT NULL,
  `outer_host` varchar(128) NOT NULL DEFAULT '' COMMENT '外网可访问地址',
  `outer_port` varchar(31) NOT NULL DEFAULT '' COMMENT '中间件默认开放端口号',
  `is_multi_version` tinyint NOT NULL DEFAULT '0' COMMENT '是否支持多版本，0:否，1:是',
  `is_default_version` tinyint NOT NULL DEFAULT '0' COMMENT '是否支持默认版本，0: 不是默认版本，1: 默认版本',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for middleware_instance
-- ----------------------------
DROP TABLE IF EXISTS `middleware_instance`;
CREATE TABLE `middleware_instance` (
  `id` bigint NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `container_id` varchar(255) DEFAULT NULL,
  `docker_launch_env` varchar(255) DEFAULT NULL,
  `host_bind_port` int NOT NULL,
  `status` varchar(30) DEFAULT NULL,
  `user_env` varchar(2048) DEFAULT NULL,
  `config_id` bigint NOT NULL,
  `parent_docker_id` bigint NOT NULL,
  `server_id` bigint DEFAULT NULL,
  `deleted` bit(1) NOT NULL,
  `resources_limit` json DEFAULT NULL,
  `status_new` varchar(255) DEFAULT NULL,
  `middle_type` varchar(45) NOT NULL DEFAULT '' COMMENT '中间件类型：mysql，redis，postgresql，mongo',
  `middle_user` varchar(255) DEFAULT '',
  `middle_passwd` varchar(255) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FKtlucvujtg6fcklucsjs8yygnh` (`config_id`) USING BTREE,
  KEY `FK3n9amd189u82rwb56ck083vhy` (`parent_docker_id`) USING BTREE,
  KEY `FK5swglb7f5bvinfsjojwrw1o45` (`server_id`) USING BTREE,
  CONSTRAINT `FK3n9amd189u82rwb56ck083vhy` FOREIGN KEY (`parent_docker_id`) REFERENCES `docker_container` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK5swglb7f5bvinfsjojwrw1o45` FOREIGN KEY (`server_id`) REFERENCES `docker_server` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKtlucvujtg6fcklucsjs8yygnh` FOREIGN KEY (`config_id`) REFERENCES `middleware_config` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for middleware_run_cmd_log
-- ----------------------------
DROP TABLE IF EXISTS `middleware_run_cmd_log`;
CREATE TABLE `middleware_run_cmd_log` (
  `id` bigint NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `cmd` text,
  `end_of_run` bit(1) NOT NULL,
  `run_id` varchar(255) DEFAULT NULL,
  `middleware_instance_id` bigint DEFAULT NULL,
  `middleware_run_type_enum` varchar(255) DEFAULT NULL,
  `deleted` bit(1) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FK3giofw2ymqdlxli0t5vvt5on1` (`middleware_instance_id`) USING BTREE,
  CONSTRAINT `FK3giofw2ymqdlxli0t5vvt5on1` FOREIGN KEY (`middleware_instance_id`) REFERENCES `middleware_instance` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for nix_pkg
-- ----------------------------
DROP TABLE IF EXISTS `nix_pkg`;
CREATE TABLE `nix_pkg` (
  `id` bigint NOT NULL,
  `deleted` bit(1) NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `UK_4cp5w4o5n0weivxtvhvle7anr` (`name`) USING BTREE,
  UNIQUE KEY `UK_5k8a309ytoqxtnu28wsr4mneo` (`name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for paas_user
-- ----------------------------
DROP TABLE IF EXISTS `paas_user`;
CREATE TABLE `paas_user` (
  `id` bigint NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `phone_number` varchar(255) DEFAULT NULL,
  `deleted` bit(1) NOT NULL,
  `paas_user_identity_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FKk04hes1a6t6qo8xqlgrteeemp` (`paas_user_identity_id`) USING BTREE,
  CONSTRAINT `FKk04hes1a6t6qo8xqlgrteeemp` FOREIGN KEY (`paas_user_identity_id`) REFERENCES `paas_user_identity` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for paas_user_email_list
-- ----------------------------
DROP TABLE IF EXISTS `paas_user_email_list`;
CREATE TABLE `paas_user_email_list` (
  `paas_user_id` bigint NOT NULL,
  `email_list` varchar(255) DEFAULT NULL,
  KEY `FKlc0ps2mjsod5wc6c0gwim6ad9` (`paas_user_id`) USING BTREE,
  CONSTRAINT `FKlc0ps2mjsod5wc6c0gwim6ad9` FOREIGN KEY (`paas_user_id`) REFERENCES `paas_user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for paas_user_identity
-- ----------------------------
DROP TABLE IF EXISTS `paas_user_identity`;
CREATE TABLE `paas_user_identity` (
  `id` bigint NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `deleted` bit(1) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for paas_user_identity_paas_users
-- ----------------------------
DROP TABLE IF EXISTS `paas_user_identity_paas_users`;
CREATE TABLE `paas_user_identity_paas_users` (
  `paas_user_identity_id` bigint NOT NULL,
  `paas_users_id` bigint NOT NULL,
  UNIQUE KEY `UK_11p2usw25yr00xc97b4v33033` (`paas_users_id`) USING BTREE,
  KEY `FKqv0t7u7sy9w2pt0wudpx5st10` (`paas_user_identity_id`) USING BTREE,
  CONSTRAINT `FKi8qexie6rpva3imu0kglutk70` FOREIGN KEY (`paas_users_id`) REFERENCES `paas_user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKqv0t7u7sy9w2pt0wudpx5st10` FOREIGN KEY (`paas_user_identity_id`) REFERENCES `paas_user_identity` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for paas_user_tenant_user_list
-- ----------------------------
DROP TABLE IF EXISTS `paas_user_tenant_user_list`;
CREATE TABLE `paas_user_tenant_user_list` (
  `paas_user_id` bigint NOT NULL,
  `tenant_user_list_id` bigint NOT NULL,
  UNIQUE KEY `UK_gvomrt16m52nm6uovf870x6x2` (`tenant_user_list_id`) USING BTREE,
  KEY `FK4vpr0uwfherqjgl2wmy6sftkq` (`paas_user_id`) USING BTREE,
  CONSTRAINT `FK1d0tbdmn5dxuoxqv14wh16cea` FOREIGN KEY (`tenant_user_list_id`) REFERENCES `tenant_user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK4vpr0uwfherqjgl2wmy6sftkq` FOREIGN KEY (`paas_user_id`) REFERENCES `paas_user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for playground
-- ----------------------------
DROP TABLE IF EXISTS `playground`;
CREATE TABLE `playground` (
  `id` bigint NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `bind_object_id` bigint DEFAULT NULL,
  `bind_type` varchar(255) DEFAULT NULL,
  `deleted` bit(1) NOT NULL,
  `status` varchar(255) NOT NULL,
  `tenant_id` bigint NOT NULL,
  `docker_container_id` bigint DEFAULT NULL,
  `ide_server_id` bigint DEFAULT NULL,
  `code_zone_id` bigint DEFAULT NULL,
  `code_zone_snapshot_id` bigint DEFAULT NULL,
  `env` json DEFAULT NULL,
  `status_new` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `docker_container_unique` (`docker_container_id`) USING BTREE,
  KEY `IDXjfqyd67pva221cbrhw0kndxcq` (`status`) USING BTREE,
  KEY `FKhf3sm9s1ghx0btftnqrhvlyrw` (`docker_container_id`) USING BTREE,
  KEY `FKlej5r92c82g086wyoq7hd3xg8` (`ide_server_id`) USING BTREE,
  KEY `FKm5e7kpsaroki3yxqh4nxg5pof` (`code_zone_id`) USING BTREE,
  KEY `FKdb6008l60o6t3w341jb9i8sph` (`code_zone_snapshot_id`) USING BTREE,
  CONSTRAINT `FKdb6008l60o6t3w341jb9i8sph` FOREIGN KEY (`code_zone_snapshot_id`) REFERENCES `code_zone_snapshot` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKhf3sm9s1ghx0btftnqrhvlyrw` FOREIGN KEY (`docker_container_id`) REFERENCES `docker_container` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKlej5r92c82g086wyoq7hd3xg8` FOREIGN KEY (`ide_server_id`) REFERENCES `ide_server` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKm5e7kpsaroki3yxqh4nxg5pof` FOREIGN KEY (`code_zone_id`) REFERENCES `code_zone` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for playground_bind_log
-- ----------------------------
DROP TABLE IF EXISTS `playground_bind_log`;
CREATE TABLE `playground_bind_log` (
  `id` bigint NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `bind_object_id` bigint NOT NULL,
  `bind_time` datetime DEFAULT NULL,
  `bind_type` varchar(255) NOT NULL,
  `docker_container_id` bigint NOT NULL,
  `playground_id` bigint NOT NULL,
  `deleted` bit(1) NOT NULL,
  `default_open_file` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FK3macvjrn0829dj7dp7lacblqx` (`playground_id`) USING BTREE,
  CONSTRAINT `FK3macvjrn0829dj7dp7lacblqx` FOREIGN KEY (`playground_id`) REFERENCES `playground` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for playground_bind_logs
-- ----------------------------
DROP TABLE IF EXISTS `playground_bind_logs`;
CREATE TABLE `playground_bind_logs` (
  `playground_id` bigint NOT NULL,
  `bind_logs_id` bigint NOT NULL,
  UNIQUE KEY `UK_h3b3p3j4s9c6pgas5yndr6sqa` (`bind_logs_id`) USING BTREE,
  KEY `FKnf4oi9joa72kq8rnywgbmdopn` (`playground_id`) USING BTREE,
  CONSTRAINT `FK64s9qwcvim47uffjn6pq8kqor` FOREIGN KEY (`bind_logs_id`) REFERENCES `playground_bind_log` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKnf4oi9joa72kq8rnywgbmdopn` FOREIGN KEY (`playground_id`) REFERENCES `playground` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for playground_record_log
-- ----------------------------
DROP TABLE IF EXISTS `playground_record_log`;
CREATE TABLE `playground_record_log` (
  `id` bigint NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `bind_object_id` bigint NOT NULL,
  `bind_time` datetime DEFAULT NULL,
  `bind_type` varchar(255) NOT NULL,
  `playground_id` bigint NOT NULL,
  `deleted` bit(1) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FK38t36n696eyfg4yepfi859mx7` (`playground_id`) USING BTREE,
  CONSTRAINT `FK38t36n696eyfg4yepfi859mx7` FOREIGN KEY (`playground_id`) REFERENCES `playground` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for run_cmd_log
-- ----------------------------
DROP TABLE IF EXISTS `run_cmd_log`;
CREATE TABLE `run_cmd_log` (
  `id` bigint NOT NULL,
  `deleted` bit(1) NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `cmd` text,
  `end_of_run` bit(1) NOT NULL,
  `run_id` varchar(255) DEFAULT NULL,
  `docker_container_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FKpyvur30p313752anp12qoedci` (`docker_container_id`) USING BTREE,
  CONSTRAINT `FKpyvur30p313752anp12qoedci` FOREIGN KEY (`docker_container_id`) REFERENCES `docker_container` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for ssh_private_keys
-- ----------------------------
DROP TABLE IF EXISTS `ssh_private_keys`;
CREATE TABLE `ssh_private_keys` (
  `id` bigint NOT NULL COMMENT '主键',
  `private_key` varchar(5200) COLLATE utf8mb4_general_ci NOT NULL COMMENT '私钥',
  `key_type` tinyint NOT NULL DEFAULT '1' COMMENT 'Key类型(1 服务端私钥 2HostKey)',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `created_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='ssh服务端证书';

-- ----------------------------
-- Table structure for ssh_user_authorized_keys
-- ----------------------------
DROP TABLE IF EXISTS `ssh_user_authorized_keys`;
CREATE TABLE `ssh_user_authorized_keys` (
  `id` bigint NOT NULL COMMENT '主键',
  `user_id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户ID',
  `username` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户名',
  `title` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标题',
  `public_key` varchar(5200) COLLATE utf8mb4_general_ci NOT NULL COMMENT '公钥',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `created_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='ssh用户证书';

-- ----------------------------
-- Table structure for storage_resource
-- ----------------------------
DROP TABLE IF EXISTS `storage_resource`;
CREATE TABLE `storage_resource` (
  `id` bigint NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `root_path` varchar(255) DEFAULT NULL,
  `deleted` bit(1) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for tenant
-- ----------------------------
DROP TABLE IF EXISTS `tenant`;
CREATE TABLE `tenant` (
  `id` bigint NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `code` varchar(255) NOT NULL,
  `config_file_name` varchar(255) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `secret` varchar(255) NOT NULL,
  `deleted` bit(1) NOT NULL,
  `tenant_config_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `UK_40na76d811fvgelpf3re932jx` (`code`) USING BTREE,
  UNIQUE KEY `UK_dcxf3ksi0gyn1tieeq0id96lm` (`name`) USING BTREE,
  UNIQUE KEY `UK_ttq79bhotkod7jdn94grv1k5` (`secret`) USING BTREE,
  KEY `FKatte9ntdcffx9s00ooylxqdgd` (`tenant_config_id`) USING BTREE,
  KEY `IDX4dh0u5orbgi8smpbqnjtjra6m` (`deleted`,`code`) USING BTREE,
  CONSTRAINT `FKatte9ntdcffx9s00ooylxqdgd` FOREIGN KEY (`tenant_config_id`) REFERENCES `tenant_config` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for tenant_config
-- ----------------------------
DROP TABLE IF EXISTS `tenant_config`;
CREATE TABLE `tenant_config` (
  `id` bigint NOT NULL,
  `deleted` bit(1) NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `cover_gitignore` bit(1) DEFAULT NULL,
  `file_tree_ignore` varchar(255) DEFAULT NULL,
  `resource_monitoring` bit(1) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for tenant_user
-- ----------------------------
DROP TABLE IF EXISTS `tenant_user`;
CREATE TABLE `tenant_user` (
  `id` bigint NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `phone_number` varchar(255) DEFAULT NULL,
  `user_id` varchar(255) DEFAULT NULL,
  `tenant_id` bigint NOT NULL,
  `deleted` bit(1) NOT NULL,
  `paas_user_id` bigint NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `UK_tenant_user` (`deleted`,`tenant_id`,`user_id`) USING BTREE,
  KEY `FKjbyohwto7pt48xywupgf4vjc7` (`tenant_id`) USING BTREE,
  KEY `deleted_tenant_user` (`tenant_id`,`user_id`) USING BTREE,
  KEY `index_2` (`paas_user_id`) USING BTREE,
  KEY `IDXt41ilpgtoklgm8vf17sgwiods` (`paas_user_id`) USING BTREE,
  CONSTRAINT `FKcixdri1pd5gtnglryq6oux0tx` FOREIGN KEY (`paas_user_id`) REFERENCES `paas_user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKjbyohwto7pt48xywupgf4vjc7` FOREIGN KEY (`tenant_id`) REFERENCES `tenant` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for unit_test_framework
-- ----------------------------
DROP TABLE IF EXISTS `unit_test_framework`;
CREATE TABLE `unit_test_framework` (
  `id` bigint NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `default_framework` bit(1) NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `environment_id` bigint NOT NULL,
  `deleted` bit(1) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FKdldgjesejmem63ufpx79bfs2c` (`environment_id`) USING BTREE,
  CONSTRAINT `FKdldgjesejmem63ufpx79bfs2c` FOREIGN KEY (`environment_id`) REFERENCES `environment` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for unit_test_run_log
-- ----------------------------
DROP TABLE IF EXISTS `unit_test_run_log`;
CREATE TABLE `unit_test_run_log` (
  `id` bigint NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `end_of_run` bit(1) NOT NULL,
  `method_name` varchar(255) DEFAULT NULL,
  `path` varchar(255) DEFAULT NULL,
  `run_id` varchar(255) DEFAULT NULL,
  `docker_container_id` bigint DEFAULT NULL,
  `deleted` bit(1) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FK88l6ar1lpks761t1vebsiv3me` (`docker_container_id`) USING BTREE,
  KEY `IDX47779kj5dwedd597y6pu38upv` (`deleted`,`run_id`) USING BTREE,
  KEY `index_0` (`run_id`,`end_of_run`) USING BTREE,
  CONSTRAINT `FK88l6ar1lpks761t1vebsiv3me` FOREIGN KEY (`docker_container_id`) REFERENCES `docker_container` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for url_resource
-- ----------------------------
DROP TABLE IF EXISTS `url_resource`;
CREATE TABLE `url_resource` (
  `id` bigint NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `status` varchar(255) NOT NULL,
  `tenant_id` bigint NOT NULL,
  `type` varchar(255) NOT NULL,
  `url` varchar(255) DEFAULT NULL,
  `deleted` bit(1) NOT NULL,
  `machine_id` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `UK12ixjy8oml1s8abties64k28q` (`url`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for url_resources
-- ----------------------------
DROP TABLE IF EXISTS `url_resources`;
CREATE TABLE `url_resources` (
  `id` bigint NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `tenant_id` bigint NOT NULL,
  `type` varchar(255) NOT NULL,
  `url` varchar(255) DEFAULT NULL,
  `deleted` bit(1) NOT NULL,
  `machine_id` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `UK12ixjy8oml1s8abties64k28q` (`url`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for url_suffix
-- ----------------------------
DROP TABLE IF EXISTS `url_suffix`;
CREATE TABLE `url_suffix` (
  `id` bigint NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL,
  `enable` bit(1) NOT NULL,
  `suffix` varchar(255) DEFAULT NULL,
  `tenant_id` bigint DEFAULT NULL,
  `deleted` bit(1) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for url_user_resource
-- ----------------------------
DROP TABLE IF EXISTS `url_user_resource`;
CREATE TABLE `url_user_resource` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `created_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint NOT NULL DEFAULT '0',
  `playground_id` varchar(64) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'playground编号',
  `docker_id` varchar(64) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '容器编号',
  `url` varchar(512) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '固定url地址',
  `deleted` bit(1) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1926490606255726593 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='固定url资源';

SET FOREIGN_KEY_CHECKS = 1;
