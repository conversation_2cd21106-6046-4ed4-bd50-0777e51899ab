package com.dao42.paas.config.properties;

import lombok.Data;

/**
 * BTRFS 配置
 *
 * <AUTHOR>
 */

@Data
public class Btrfs {

    /**
     * btrfs系统地址
     */
    private String host;

    /**
     * btrfs 端口
     */
    private Integer port;

    /**
     * btrfs 用户名
     */
    private String userName;

    /**
     * btrfs 私钥
     */
    private String keyPath;

    /**
     * 写入命令
     */
    private String createCmd;

    /**
     * 删除命令
     */
    private String removeCmd;
    /**
     * check磁盘快照是否在运行的文件
     */
    private String checkDiskSnapshotLockFile;
}
