package com.dao42.paas.config.qcloud;

import com.qcloud.cmq.Account;
import com.qcloud.cmq.Queue;
import com.qcloud.cmq.entity.CmqConfig;
import com.tencentcloudapi.as.v20180419.AsClient;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.Language;
import com.tencentcloudapi.cvm.v20170312.CvmClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.AwsCredentials;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.autoscaling.AutoScalingClient;
import software.amazon.awssdk.services.ec2.Ec2Client;
import software.amazon.awssdk.services.sqs.SqsClient;
import java.net.URI;

/**
 * 腾讯云COS配置
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Configuration
@ConditionalOnProperty(prefix = "q-cloud", name = {"secret-id", "secret-key"})
public class QcloudConfig {

    private final QcloudProperties qCloudProperties;

    private Credential getCredential() {
        return new Credential(qCloudProperties.getSecretId(), qCloudProperties.getSecretKey());
    }

    /**
     * 云实例api调用
     *
     * @return CvmClient
     */
    @Bean
    public CvmClient cvmClient() {
        // 实例化一个client选项，可选的，没有特殊需求可以跳过
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setDebug(true);
        clientProfile.setLanguage(Language.ZH_CN);

        return new CvmClient(getCredential(), qCloudProperties.getRegion(), clientProfile);
    }

    @Bean
    public Ec2Client ec2Client() {
        Ec2Client ec2 = Ec2Client.builder().credentialsProvider(new AwsCredentialsProvider() {
                @Override
                public AwsCredentials resolveCredentials() {
                    AwsCredentials awsCredentials = AwsBasicCredentials.create(qCloudProperties.getSecretId(),
                        qCloudProperties.getSecretKey());
                    return awsCredentials;
                }
            })
            .region(Region.of(qCloudProperties.getRegion()))
            .build();
        return ec2;

    }

    /**
     * 弹性伸缩api调用
     *
     * @return AsClient
     */
    @Bean
    @ConditionalOnProperty(prefix = "q-cloud", name = {"as-group"})
    public AsClient asClient() {
        /* 实例化一个client选项，可选的，没有特殊需求可以跳过*/
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setDebug(true);
        clientProfile.setLanguage(Language.ZH_CN);
        log.debug("AS client use group: {}", qCloudProperties.getAsGroup());
        return new AsClient(getCredential(), qCloudProperties.getRegion(), clientProfile);
    }

    @Bean
    @ConditionalOnProperty(prefix = "q-cloud", name = {"as-group"})
    public AutoScalingClient autoScalingClient() {
        AutoScalingClient autoScalingClient = AutoScalingClient.builder()
            .credentialsProvider(new AwsCredentialsProvider() {
                @Override
                public AwsCredentials resolveCredentials() {
                    AwsCredentials awsCredentials = AwsBasicCredentials.create(qCloudProperties.getSecretId(),
                        qCloudProperties.getSecretKey());
                    return awsCredentials;
                }
            })
            .region(Region.of(qCloudProperties.getRegion()))
            .build();
        return autoScalingClient;
    }

    /**
     * CMQ队列操作对象
     *
     * @see <a href="https://cloud.tencent.com/document/product/1496/70948">【错误码-消息队列 CMQ 版-文档中心-腾讯云】</a>
     */
    @Bean
    @ConditionalOnProperty(prefix = "q-cloud", name = {"end-point", "queue-name"})
    public Queue cmqQueue() {
        CmqConfig cmqConfig = new CmqConfig();
        cmqConfig.setPrintSlow(true);
        cmqConfig.setAlwaysPrintResultLog(true);
        cmqConfig.setEndpoint(qCloudProperties.getEndPoint());
        cmqConfig.setSecretId(qCloudProperties.getSecretId());
        cmqConfig.setSecretKey(qCloudProperties.getSecretKey());
        Account account = new Account(cmqConfig);
        log.debug("CMQ queue listen on: {}", qCloudProperties.getQueueName());
        return account.getQueue(qCloudProperties.getQueueName());
    }

    @Bean
    @ConditionalOnProperty(prefix = "q-cloud", name = {"end-point", "queueUrl"})
    public SqsClient sqsClient() {
        SqsClient sqsClient = SqsClient.builder().credentialsProvider(new AwsCredentialsProvider() {
                @Override
                public AwsCredentials resolveCredentials() {
                    AwsCredentials awsCredentials = AwsBasicCredentials.create(qCloudProperties.getSecretId(),
                        qCloudProperties.getSecretKey());
                    return awsCredentials;
                }
            })
            .region(Region.of(qCloudProperties.getRegion()))
            .build();
        return sqsClient;
    }

}