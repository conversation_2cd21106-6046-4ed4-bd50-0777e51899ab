package com.dao42.paas.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

/**
 * 系统配置
 *
 * <AUTHOR>
 */
@ConfigurationProperties("system")
@Data
public class SystemProperties {

    /**
     * 系统版本号
     */
    private String version;

    /**
     * 是否测试环境
     */
    private boolean testEnv = false;

    /**
     * 是否开启本地调试
     */
    private boolean localDebug = false;

    /**
     * 是否开启token校验
     */
    private boolean tokenValidate = false;

    /**
     * SM4 密钥
     */
    private String sm4Key;

    /**
     * Docker容器
     */
    @NestedConfigurationProperty
    private Docker docker;

    /**
     * 文件系统
     */
    @NestedConfigurationProperty
    private Btrfs btrfs;

    /**
     * 代码空间
     */
    @NestedConfigurationProperty
    private CodeZone codeZone;

    /**
     * nix
     */
    @NestedConfigurationProperty
    private Nix nix;

    /**
     * playground
     */
    @NestedConfigurationProperty
    private Playground playground;

    /**
     * mq
     */
    @NestedConfigurationProperty
    private RabbitMq rabbitMq;

    /**
     * 资源
     */
    @NestedConfigurationProperty
    private Resource resource;

    @NestedConfigurationProperty
    private DockerImageRepository dockerImageRepository;

    @NestedConfigurationProperty
    private RedisRateLimter redisRateLimter;

}
