package com.dao42.paas.config.properties;

import lombok.Data;

/**
 * Docker容器配置信息
 */
@Data
public class Docker {

    /**
     * 是否开启本地调试
     */
    private boolean sockConnect;

    /**
     * 容器名称前缀
     */
    private String prefixName;

    /**
     * agent挂载配置
     */
    private Agent agent;

    /**
     * nix挂载路径
     */
    private String volumeNix;

    /**
     * 文件存储路径
     */
    private Storage storage;

    /**
     * paas agent 使用的 gpt 变量
     */
    private GPT gpt;

    /**
     * docker 资源限制
     */
    private Limit limit;

    /**
     * goAgent 使用的网关地址
     */
    private String paasGatewayUrl;

    @Data
    public static class Storage {

        /**
         * 文件服务器中的目录
         */
        private String fileServerPath;

        /**
         * 共享盘中的目录
         */
        private String nfsPath;

        /**
         * 容器中的home目录
         */
        private String homePath;

        /**
         * 容器中项目目录
         */
        private String appPath;

        /**
         * 代码存储路径
         */
        private String codePath;

        private String ragPath;

    }

    @Data
    public static class Limit {

        /**
         * docker 限制每秒操作文件数
         */
        private Long iops;

        /**
         * docker 限制吞吐(M)
         */
        private Long bandwidth;

        /**
         * docker 限制磁盘空间
         */
        private String storageSize;

    }

    @Data
    public static class Agent {

        /**
         * agent在宿主机的路径
         */
        private String hostPath;

        /**
         * agent挂载到容器的路径
         */
        private String dockerPath;

    }

    @Data
    public static class GPT{

        private String gptId;

        private String gptKey;

        private String proxyUrl;

        private String authToken;
    }

}
