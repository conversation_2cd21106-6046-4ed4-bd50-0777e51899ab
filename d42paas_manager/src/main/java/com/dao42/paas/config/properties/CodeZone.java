package com.dao42.paas.config.properties;

import lombok.Data;

/**
 * 代码空间配置
 *
 * <AUTHOR>
 */
@Data
public class CodeZone {

    /**
     * 文件服务上的meta路径
     */
    private String meta;

    /**
     * env config path
     */
    private String envPath;

    /**
     * 每个codeZone下代码路径名称
     */
    private String sourcePathName;

    /**
     * 每个codeZone下依赖路径名称
     */
    private String dependencyPathName;

    /**
     * CodeZone on Manager-Docker
     */
    private String path;

    /**
     * CodeZone on Btrfs
     */
    private String pathInBtrfs;

    /**
     * CodeZoneSnapshot on Manager-docker
     */
    private String snapshotPath;

    /**
     * CodeZoneSnapshot on Btrfs
     */
    private String snapshotPathInBtrfs;

    /**
     * 存放下载文件的路径
     */
    private String download;

    /**
     * 存放上传文件的路径
     */
    private String importFile;
    
    /**
     * github代理
     */
    private String githubProxy;

    private String blockedFiles;
    
    /**
     * CodeZone的默认过期天数
     */
    private int defaultExpireDays = 30;
    
    /**
     * 批量删除的速率限制(每分钟)
     * 控制批量删除操作的速率，避免过快删除导致系统负载过高
     */
    private int maxDeleteRatePerSecond = 10;
}