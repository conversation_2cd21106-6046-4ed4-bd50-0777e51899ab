package com.dao42.paas.config.properties;

import lombok.Data;

@Data
public class RabbitMq {

    /**
     * 消息的生存时间(毫秒）。超时未处理将进入alter-exchange
     */
    private int messageTtl;

    private String exchange;

    private String alterExchange;

    /**
     * manager接收消息的队列
     */
    private Queue queue;

    @Data
    public static class Queue {

        private String manager;

        private String playground;

        private String exception;

    }

}
