package com.dao42.paas.config.qcloud;

import cn.hutool.json.JSONUtil;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 腾讯云COS配置参数
 *
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "q-cloud")
public class QcloudProperties {

    private String secretId;
    private String secretKey;
    /**
     * 接入区域
     */
    private String region;

    /**
     * 伸缩组ID
     */
    private String asGroup;

    /**
     * CMQ队列名，用于接收弹性伸缩的生命周期钩子
     */
    private String queueName;

    private String queueUrl;

    /**
     * CMQ接入点
     */
    private String endPoint;


    @Override
    public String toString() {
        return JSONUtil.toJsonStr(this);
    }
}
