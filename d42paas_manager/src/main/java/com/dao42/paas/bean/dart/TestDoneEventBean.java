package com.dao42.paas.bean.dart;

import lombok.Getter;
import lombok.Setter;

/**
 * 单个测试用例完成事件
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class TestDoneEventBean {

    /**
     * 事件类型，值一定为 testDone
     */
    private String type;
    /**
     * 测试 id，指向 testStart 事件的 id
     */
    private Integer testID;
    /**
     * 测试结果:
     * - success: 断言成功
     * - failure: 断言失败
     * - error: 测试有 TestFailure 以外的错误，如编译错误
     */
    private String result;
    /**
     * 自测试运行程序启动以来经过的时间（以毫秒为单位）
     */
    private Long time;
}
