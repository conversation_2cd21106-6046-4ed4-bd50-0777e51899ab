package com.dao42.paas.bean.ginkgo;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class GinkgoTestResultsBean {

    private String suitePath;

    private String suiteDescription;

    private Boolean suiteSucceeded;

    private Long runTime;

    private List<GinkgoSpecReportsBean> specReports = new ArrayList<>();
}
