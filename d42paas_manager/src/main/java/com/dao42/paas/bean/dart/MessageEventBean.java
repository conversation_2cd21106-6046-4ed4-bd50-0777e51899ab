package com.dao42.paas.bean.dart;

import lombok.Getter;
import lombok.Setter;

/**
 * 控制台打印事件
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class MessageEventBean {
    /**
     * 事件类型，该事件类型 type 值为 print
     */
    private String type;
    /**
     * 测试 id，指向 testStart 事件的 id
     */
    private Integer testID;
    /**
     * 消息类型，一般为 print。
     */
    private String messageType;
    /**
     * 控制台打印的内容
     */
    private String message;
    /**
     * 自测试运行程序启动以来经过的时间（以毫秒为单位）
     */
    private Long time;
}
