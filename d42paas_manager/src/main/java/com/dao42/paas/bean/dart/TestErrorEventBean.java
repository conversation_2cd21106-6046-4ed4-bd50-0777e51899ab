package com.dao42.paas.bean.dart;

import lombok.Getter;
import lombok.Setter;

/**
 * 测试用例发生错误事件，记录错误信息：如断言失败或编译出错信息
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class TestErrorEventBean {

    /**
     * 事件类型，值一定为 error
     */
    private String type;
    /**
     * 测试 id，指向 testStart 事件的 id
     */
    private Integer testID;
    /**
     * 错误信息
     */
    private String error;
    /**
     * 错误的堆栈跟踪
     */
    private String stackTrace;
    /**
     * 错误是否为 TestFailure
     * true     代表是断言失败
     * false    则是未捕获的错误
     */
    private Boolean isFailure;
    /**
     * 自测试运行程序启动以来经过的时间（以毫秒为单位）
     */
    private Long time;
}
