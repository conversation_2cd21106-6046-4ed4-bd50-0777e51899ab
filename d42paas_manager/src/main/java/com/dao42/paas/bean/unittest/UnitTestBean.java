package com.dao42.paas.bean.unittest;

import com.dao42.paas.enums.PlaygroundBindType;
import com.dao42.paas.model.Playground;
import com.dao42.paas.model.docker.DockerContainer;
import lombok.Getter;
import lombok.Setter;


/**
 * UnitTestBean
 */
@Getter
@Setter
public class UnitTestBean {

    private String language;
    private String appPath;
    private String rootPath;
    private DockerContainer dockerContainer;
    private String frameworkName;
    private Playground playground;
    // ootbImageId or codeZoneId
    private PlaygroundBindType bindType;

}
