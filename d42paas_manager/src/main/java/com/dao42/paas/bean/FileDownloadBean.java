package com.dao42.paas.bean;

import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * FileDownloadBean
 */
@Getter
@Setter
public class FileDownloadBean {

    private String fileName;

    private String path;

    private List<String> ignoreList;

    public FileDownloadBean(String fileName, String path, List<String> ignoreList) {
        this.fileName = fileName;
        this.path = path;
        this.ignoreList = ignoreList;
    }
}
