package com.dao42.paas.bean.dart;

import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * 一个单元测试运行结果应该包含的完整数据
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class TestResultBean {

    /**
     * 测试开始事件
     */
    private TestStartEventBean testStart;
    /**
     * 该测试可能的控制台打印事件（用户 print 行为）
     */
    private List<MessageEventBean> messageList;
    /**
     * 测试用例失败事件
     */
    private TestErrorEventBean testError;
    /**
     * 该测试用例完成事件
     */
    private TestDoneEventBean testDone;

    public TestResultBean(TestStartEventBean testStart, List<MessageEventBean> messageList,
        TestErrorEventBean testError, TestDoneEventBean testDone) {
        this.testStart = testStart;
        this.messageList = messageList;
        this.testError = testError;
        this.testDone = testDone;
    }

    public TestResultBean() {
    }
}
