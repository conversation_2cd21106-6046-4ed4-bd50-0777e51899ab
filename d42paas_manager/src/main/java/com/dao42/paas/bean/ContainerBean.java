package com.dao42.paas.bean;

import com.github.dockerjava.api.model.Container;
import lombok.Getter;
import lombok.Setter;

/**
 * ContainerBean
 */
@Deprecated
@Getter
@Setter
public class ContainerBean {

    public ContainerBean() {}
    public ContainerBean(boolean success, Container container) {
        this.success = success;
        this.container = container;
    }

    private Container container;

    private boolean success;

}
