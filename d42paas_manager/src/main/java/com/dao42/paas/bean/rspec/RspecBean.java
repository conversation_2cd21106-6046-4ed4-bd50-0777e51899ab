package com.dao42.paas.bean.rspec;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @create 2022/9/15 5:01 下午
 */
@NoArgsConstructor
@Data
public class RspecBean {

    @JsonProperty("version")
    private String version;
    @JsonProperty("examples")
    private List<ExamplesDTO> examples;
    @JsonProperty("summary")
    private SummaryDTO summary;
    @JsonProperty("summary_line")
    private String summaryLine;

    @NoArgsConstructor
    @Data
    public static class SummaryDTO {

        @JsonProperty("duration")
        private Double duration;
        @JsonProperty("example_count")
        private Integer exampleCount;
        @JsonProperty("failure_count")
        private Integer failureCount;
        @JsonProperty("pending_count")
        private Integer pendingCount;
        @JsonProperty("errors_outside_of_examples_count")
        private Integer errorsOutsideOfExamplesCount;
    }

    @NoArgsConstructor
    @Data
    public static class ExamplesDTO {

        @JsonProperty("id")
        private String id;
        @JsonProperty("description")
        private String description;
        @JsonProperty("full_description")
        private String fullDescription;
        @JsonProperty("status")
        private String status;
        @JsonProperty("file_path")
        private String filePath;
        @JsonProperty("line_number")
        private Integer lineNumber;
        @JsonProperty("run_time")
        private Double runTime;
        @JsonProperty("pending_message")
        private Object pendingMessage;
        @JsonProperty("exception")
        private ExceptionDTO exception;

        @NoArgsConstructor
        @Data
        public static class ExceptionDTO {

            @JsonProperty("class")
            private String classX;
            @JsonProperty("message")
            private String message;
            @JsonProperty("backtrace")
            private List<String> backtrace;
        }
    }
}
