package com.dao42.paas.common;

import javax.validation.constraints.Max;
import javax.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 资源限制
 */
@Getter
@Setter
@AllArgsConstructor
@ToString
public class ResourcesLimit {

    @Positive
    private int CPU;

    /**
     * 内存，单位MB
     */
    @Max(value = 5 * 1024, message = "docker 调整内存最大为 5G")
    @Positive
    private long RAM;

    /**
     * 磁盘空间，单位MB
     */
    private long DISK;

}
