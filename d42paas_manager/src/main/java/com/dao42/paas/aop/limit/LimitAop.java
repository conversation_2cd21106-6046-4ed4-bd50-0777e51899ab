package com.dao42.paas.aop.limit;

import com.google.common.util.concurrent.RateLimiter;
import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@SuppressWarnings("UnstableApiUsage")
@Slf4j
@Aspect
@Component
public class LimitAop {

    /**
     * 不同的接口，不同的流量控制 map的key为 Limiter.key
     */
    private final Map<String, RateLimiter> limitMap = new ConcurrentHashMap<>();

    public static final String SELECT_SERVER = "findServerAndGetResources";

    @Around("@annotation(com.dao42.paas.aop.limit.Limit)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        // 再次检查方法注解（可省略）
        Limit limit = method.getAnnotation(Limit.class);
        if (limit == null) {
            return joinPoint.proceed();
        }

        // key作用：不同的接口，不同的流量控制
        StringBuilder limitKey = new StringBuilder(limit.key());

        // 判断是否有带注解参数(不同的参数生成的限流不同)
        Annotation[][] parameterAnnotations = method.getParameterAnnotations();
        Object[] args = joinPoint.getArgs();
        assert args.length == parameterAnnotations.length;
        for (int i = 0; i < parameterAnnotations.length; i++) {
            Annotation[] annotations = parameterAnnotations[i];
            if (Arrays.stream(annotations).anyMatch(e -> e.annotationType() == LimitParameter.class)) {
                Object param = args[i];
                limitKey.append(":").append(param);
            }
        }

        RateLimiter rateLimiter;
        // 验证缓存是否有命中key
        if (!limitMap.containsKey(limitKey.toString())) {
            // 创建令牌桶
            rateLimiter = RateLimiter.create(limit.permitsPerSecond());
            limitMap.put(limitKey.toString(), rateLimiter);
            log.debug("新建令牌桶={}，容量={}", limitKey, limit.permitsPerSecond());
        }
        rateLimiter = limitMap.get(limitKey.toString());

        // 阻塞，直到获取令牌
        double acquire = rateLimiter.acquire();
        log.debug("令牌桶={}，获取耗时={}", limitKey, acquire);

        return joinPoint.proceed();
    }

    /**
     * 更新速率限制
     *
     * @param key
     * @param permitsPerSecond
     */
    @Deprecated
    public void updateLimitRate(String key, double permitsPerSecond) {
        if (permitsPerSecond <= 0) {
            permitsPerSecond = 1;
        }
        RateLimiter rateLimiter;
        // 验证缓存是否有命中key
        if (!limitMap.containsKey(key)) {
            // 创建令牌桶
            rateLimiter = RateLimiter.create(permitsPerSecond);
            limitMap.put(key, rateLimiter);
            log.debug("新建令牌桶={}，容量={}", key, permitsPerSecond);
        }
        rateLimiter = limitMap.get(key);
        rateLimiter.setRate(permitsPerSecond);
        log.debug("更新令牌桶={}，容量={}", key, permitsPerSecond);
    }

}