package com.dao42.paas.aop.log;

import com.dao42.paas.aop.log.annotation.Did;
import com.dao42.paas.aop.log.annotation.Pid;
import com.dao42.paas.constants.MdcConstant;
import com.dao42.paas.framework.alert.ExceptionMsgBot;
import com.dao42.paas.framework.alert.ExceptionMsgBotBean;
import com.dao42.paas.model.Playground;
import com.dao42.paas.model.docker.DockerContainer;
import java.lang.annotation.Annotation;
import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * 添加日志上下文信息
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Order(0)
@Component
public class CustomLogPropsAspect {

    @Autowired
    private ExceptionMsgBot bot;

    /**
     * 注入日志追踪信息
     *
     * @param joinPoint
     * @return
     * @throws Throwable
     */
    @Around("execution(public * com.dao42.paas..*.*(.., @com.dao42.paas.aop.log.annotation.Pid (*), ..)) " +
        "|| execution(public * com.dao42.paas..*.*(.., @com.dao42.paas.aop.log.annotation.Did (*), ..))"
    )
    public Object setLogTraceArgs(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Annotation[][] parameterAnnotations = signature.getMethod().getParameterAnnotations();

        Object[] args = joinPoint.getArgs();
        assert args.length == parameterAnnotations.length;

        for (int i = 0; i < parameterAnnotations.length; i++) {
            Annotation[] annotations = parameterAnnotations[i];
            if (Arrays.stream(annotations).anyMatch(e -> e.annotationType() == Pid.class)) {
                Object param = args[i];
                if (param.getClass() == Long.class) {
                    MDC.put(MdcConstant.PLAYGROUND_ID, String.valueOf(param));
                    break;
                }
                if (param.getClass() == Playground.class) {
                    MDC.put(
                            MdcConstant.PLAYGROUND_ID,
                            String.valueOf(((Playground) param).getId()));
                    break;
                }
            }
            if (Arrays.stream(annotations).anyMatch(e -> e.annotationType() == Did.class)) {
                Object param = args[i];
                if (param.getClass() == Long.class) {
                    MDC.put(MdcConstant.DOCKER_ID, String.valueOf(param));
                    break;
                }
                if (param.getClass() == DockerContainer.class) {
                    MDC.put(
                            MdcConstant.DOCKER_ID,
                            String.valueOf(((DockerContainer) param).getId()));
                    break;
                }
            }
        }

        Object object;
        try {
            // 调用目标方法
            object = joinPoint.proceed();
            return object;
        } finally {
            MDC.clear();
        }
    }

    @Pointcut("execution(* com.dao42.paas.external..*Controller.*(..))")
    public void controllerPointCut() {
    }

    @Around(value = "controllerPointCut()")
    private Object Around(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        String className = proceedingJoinPoint.getSignature().getDeclaringType().getSimpleName();
        String methodName = proceedingJoinPoint.getSignature().getName();
        log.info("controllerPointCut,package: {}, method: {}", className, methodName);
        Object object;
        try {
            // 调用目标方法
            object = proceedingJoinPoint.proceed();
            return object;
        } finally {
            MDC.clear();
        }
    }

    /**
     * 全局异常捕获
     *
     * @param ex
     * @return
     */
    @AfterThrowing(pointcut = "execution(* com.dao42.paas.rabbit..*(..))", throwing = "ex")
    public void logAfterThrowingAllMethods(Exception ex) {
        RequestAttributes ra = RequestContextHolder.getRequestAttributes();
        ServletRequestAttributes sra = (ServletRequestAttributes) ra;
        ExceptionMsgBotBean exceptionMsgBotBean = new ExceptionMsgBotBean();
        if (sra != null) {
            HttpServletRequest request = sra.getRequest();
            exceptionMsgBotBean.setRequest(request);
        }
        exceptionMsgBotBean.setException(ex);
        bot.send(exceptionMsgBotBean);
    }

}