name: Deploy Admin

on:
  push:
    branches:
      - feature

jobs:
  build-java:
    uses: ./.github/workflows/build-java.yml

  build-docker:
    needs:
      - build-java
    runs-on: ubuntu-latest
    steps:
      - name: Download Jar and <PERSON>erfile
        uses: actions/download-artifact@v4
        with:
          name: Jar-and-Dockerfile

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1

      - name: Docker meta Admin
        id: meta-admin
        uses: docker/metadata-action@v3
        with:
          images: yingxuan-docker.pkg.coding.net/1024/paas/admin
          tags: |
            type=ref,event=branch
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha
          flavor: |
            latest=auto

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v1
        with:
          registry: yingxuan-docker.pkg.coding.net
          username: ${{ secrets.CODING_USER }}
          password: ${{ secrets.CODING_TOKEN }}

      - name: Build and push Admin
        uses: docker/build-push-action@v2
        timeout-minutes: 5
        with:
          context: d42paas_admin
          file: d42paas_admin/Dockerfile
          push: true
          tags: ${{ steps.meta-admin.outputs.tags }}
          labels: ${{ steps.meta-admin.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  deploy-feature:
    needs:
      - build-docker
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/feature'
    steps:
      - name: Deploy FEATURE
        run: |
          webhooks=(${{ secrets.WEBHOOK_FEATURE_BACKEND_ADMIN }})
          for url in ${webhooks[@]}; do
            curl -X POST $url;
          done
      - name: Webhook supporting
        run: >
          curl https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=${{ secrets.WECOM_KEY }} \
            -H "Content-Type: application/json" \
            -d "{\"msgtype\": \"markdown\",
                  \"markdown\": {
                    \"content\": \"PaaS-Admin **Feature** [正在部署]($GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID)\n> 分支: ${{ github.ref }}  \n> SHA: [${{ github.sha }}](${{ github.event.compare }})  \n> 触发人: [${{ github.event.sender.login }}](${{ github.event.sender.html_url }})\"
                  }
                }"

  deploy-develop:
    needs:
      - build-docker
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    steps:
      - name: Deploy Develop
        run: |
          webhooks=(${{ secrets.WEBHOOK_DEVELOP_BACKEND_ADMIN }})
          for url in ${webhooks[@]}; do
            curl -X POST $url;
          done
      - name: Webhook supporting
        run: >
          curl https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=${{ secrets.WECOM_KEY }} \
            -H "Content-Type: application/json" \
            -d "{\"msgtype\": \"markdown\",
                  \"markdown\": {
                    \"content\": \"PaaS-Admin **Develop** [正在部署]($GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID)\n> 分支: ${{ github.ref }}  \n> SHA: [${{ github.sha }}](${{ github.event.compare }})  \n> 触发人: [${{ github.event.sender.login }}](${{ github.event.sender.html_url }})\"
                  }
                }"

  deploy-staging:
    needs:
      - build-docker
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/staging'
    steps:
      - name: Deploy Staging
        run: |
          webhooks=(${{ secrets.WEBHOOK_STAGING_BACKEND_ADMIN }})
          for url in ${webhooks[@]}; do
            curl -X POST $url;
          done
      - name: Notify WeCom
        run: >
          curl https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=${{ secrets.WECOM_KEY }} \
            -H "Content-Type: application/json" \
            -d "{\"msgtype\": \"markdown\",
                  \"markdown\": {
                    \"content\": \"PaaS-Admin **Staging** [正在部署]($GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID)\n> 分支: ${{ github.ref }}  \n> SHA: [${{ github.sha }}](${{ github.event.compare }})  \n> 触发人: [${{ github.event.sender.login }}](${{ github.event.sender.html_url }})\"
                  }
                }"

  deploy-prod:
    needs:
      - build-docker
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/master'
    steps:
      - name: Deploy Prod
        run: |
          webhooks=(${{ secrets.WEBHOOK_PROD_BACKEND_ADMIN }})
          for url in ${webhooks[@]}; do
            curl -X POST $url;
          done
      - name: Notify WeCom
        run: >
          curl https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=${{ secrets.WECOM_KEY }} \
            -H "Content-Type: application/json" \
            -d "{\"msgtype\": \"markdown\",
                  \"markdown\": {
                    \"content\": \"PaaS-Admin **Production** [正在部署]($GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID)\n> 分支: ${{ github.ref }}  \n> SHA: [${{ github.sha }}](${{ github.event.compare }})  \n> 触发人: [${{ github.event.sender.login }}](${{ github.event.sender.html_url }})\"
                  }
                }"
