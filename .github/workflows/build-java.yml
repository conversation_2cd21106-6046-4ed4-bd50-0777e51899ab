name: Build Java

on:
  workflow_call:

jobs:
  maven:
    runs-on: ubuntu-latest
    services:
      mysql:
        image: mysql:5.7.31
        ports:
          - 3306:3306
        env:
          MYSQL_ROOT_PASSWORD: rd123456
          MYSQL_ROOT_HOST: '%'
      redis:
        image: redis:6.0.6-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
      rabbitmq:
        image: rabbitmq:3.9.9-management-alpine
        ports:
          - 5672:5672
          - 15672:15672
        env:
          RABBITMQ_DEFAULT_USER: agent
          RABBITMQ_DEFAULT_PASS: d42agent
    steps:
      - name: Checkout
        uses: actions/checkout@v2
      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'adopt'
          cache: maven
      - name: Build with Maven
        timeout-minutes: 5
        run: mvn --batch-mode --update-snapshots verify
      - name: Show Reports
        run: awk '{print}' d42paas_manager/target/surefire-reports/*.txt
      - name: Archive production artifacts
        uses: actions/upload-artifact@v4
        with:
          name: Jar-and-Dockerfile
          path: |
            */target/*.jar
            */Dockerfile
          retention-days: 1
